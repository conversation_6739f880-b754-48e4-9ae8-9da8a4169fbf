name: gantt-app

services:
  # PHP Web服务器
  web:
    build: .
    container_name: gantt_web
    ports:
      - "8080:80"
    volumes:
      - .:/var/www/html
    depends_on:
      - mysql
    environment:
      - PHP_DISPLAY_ERRORS=1
      - PHP_ERROR_REPORTING=E_ALL
    networks:
      - gantt_network

  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: gantt_mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: root123
      MYSQL_DATABASE: gantt_db
      MYSQL_USER: gantt_user
      MYSQL_PASSWORD: gantt_pass
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - gantt_network

  # phpMyAdmin
  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: gantt_phpmyadmin
    restart: always
    ports:
      - "8081:80"
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD: root123
      MYSQL_ROOT_PASSWORD: root123
    depends_on:
      - mysql
    networks:
      - gantt_network

volumes:
  mysql_data:

networks:
  gantt_network:
    driver: bridge
