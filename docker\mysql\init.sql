-- 甘特图应用数据库初始化脚本

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS gantt_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE gantt_db;

-- 创建任务表
CREATE TABLE IF NOT EXISTS tasks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    start_time DATETIME NOT NULL,
    end_time DATETIME NOT NULL,
    progress INT DEFAULT 0,
    color VARCHAR(7) DEFAULT '#3498db',
    parent_id INT DEFAULT NULL,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_start_time (start_time),
    INDEX idx_end_time (end_time),
    INDEX idx_parent_id (parent_id),
    FOREIGN KEY (parent_id) REFERENCES tasks(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建任务依赖表
CREATE TABLE IF NOT EXISTS task_dependencies (
    id INT AUTO_INCREMENT PRIMARY KEY,
    task_id INT NOT NULL,
    depends_on_id INT NOT NULL,
    dependency_type VARCHAR(50) DEFAULT 'finish_to_start',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_dependency (task_id, depends_on_id),
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
    FOREIGN KEY (depends_on_id) REFERENCES tasks(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入示例数据
INSERT INTO tasks (name, description, start_time, end_time, progress, color) VALUES
('项目启动', '项目启动会议和需求分析', '2024-01-15 09:00:00', '2024-01-15 17:00:00', 100, '#27ae60'),
('系统设计', '系统架构设计和数据库设计', '2024-01-16 09:00:00', '2024-01-18 17:00:00', 75, '#3498db'),
('前端开发', '用户界面开发和交互功能实现', '2024-01-17 09:00:00', '2024-01-22 17:00:00', 50, '#f39c12'),
('后端开发', 'API接口开发和数据库操作', '2024-01-18 09:00:00', '2024-01-23 17:00:00', 30, '#9b59b6'),
('测试部署', '系统测试和生产环境部署', '2024-01-23 09:00:00', '2024-01-25 17:00:00', 0, '#e74c3c');

-- 创建用户并授权
CREATE USER IF NOT EXISTS 'gantt_user'@'%' IDENTIFIED BY 'gantt_pass';
GRANT ALL PRIVILEGES ON gantt_db.* TO 'gantt_user'@'%';
FLUSH PRIVILEGES;
