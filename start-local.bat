@echo off
chcp 65001 >nul

echo 🚀 启动甘特图应用本地测试服务器...

REM 检查PHP是否安装
php --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ PHP未安装或未添加到PATH环境变量
    echo 请安装PHP 8.3或更高版本，或使用XAMPP/WAMP等集成环境
    pause
    exit /b 1
)

echo ✅ PHP已安装
php --version

echo.
echo 📝 注意事项：
echo    - 此模式仅用于前端界面测试
echo    - 数据库功能需要配置MySQL连接
echo    - 建议使用Docker环境进行完整测试
echo.

REM 查找可用端口
set PORT=8080
netstat -an | find ":%PORT%" >nul
if %errorlevel% equ 0 (
    set PORT=8081
    netstat -an | find ":%PORT%" >nul
    if %errorlevel% equ 0 (
        set PORT=8082
    )
)

echo 🌐 启动PHP内置服务器...
echo    地址: http://localhost:%PORT%
echo    按 Ctrl+C 停止服务器
echo.

REM 启动PHP内置服务器
php -S localhost:%PORT%

pause
