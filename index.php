<?php
/**
 * 甘特图Web应用程序主页面
 * PHP 8.3.21 兼容 - MySQL版本
 */

require_once 'config/config.php';

$config = require 'config/config.php';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $config['app']['name']; ?></title>
    <link rel="stylesheet" href="assets/css/gantt.css">
    <style>
        /* 额外的页面样式 */
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .page-title {
            flex: 1;
            min-width: 200px;
        }
        
        .page-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        @media (max-width: 768px) {
            .page-header {
                flex-direction: column;
                align-items: stretch;
            }
            
            .page-actions {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="header">
            <div class="page-header">
                <div class="page-title">
                    <h1><?php echo $config['app']['name']; ?></h1>
                    <p>专业的项目时间管理工具 - 精确到小时级别</p>
                </div>
                <div class="page-actions">
                    <span style="color: rgba(255,255,255,0.8); font-size: 0.9rem;">
                        版本 <?php echo $config['app']['version']; ?>
                    </span>
                </div>
            </div>
        </div>

        <!-- 工具栏 -->
        <div class="toolbar">
            <button class="btn btn-primary" onclick="taskModal.show()">
                ➕ 新建任务
            </button>
            <button class="btn btn-success" onclick="ganttChart.refresh()">
                🔄 刷新
            </button>
            <button class="btn btn-secondary" onclick="exportData()">
                📤 导出数据
            </button>
            <button class="btn btn-secondary" onclick="showTimeRangeModal()">
                📅 时间范围
            </button>
            
            <div style="margin-left: auto; display: flex; gap: 10px; align-items: center;">
                <span style="color: #6c757d; font-size: 0.9rem;">
                    当前时间: <span id="current-time"></span>
                </span>
            </div>
        </div>

        <!-- 甘特图容器 -->
        <div class="gantt-container">
            <div id="gantt-chart"></div>
        </div>
    </div>

    <!-- 任务编辑模态框 -->
    <div id="task-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="modal-title">新建任务</h3>
                <button class="close" onclick="taskModal.hide()">&times;</button>
            </div>
            <form id="task-form">
                <input type="hidden" id="task-id" name="id">
                
                <div class="form-group">
                    <label class="form-label" for="task-name">任务名称 *</label>
                    <input type="text" class="form-control" id="task-name" name="name" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="task-description">任务描述</label>
                    <textarea class="form-control" id="task-description" name="description" rows="3"></textarea>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label" for="task-start-time">开始时间 *</label>
                        <input type="datetime-local" class="form-control" id="task-start-time" name="start_time" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="task-end-time">结束时间 *</label>
                        <input type="datetime-local" class="form-control" id="task-end-time" name="end_time" required>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label" for="task-progress">进度 (%)</label>
                        <input type="number" class="form-control" id="task-progress" name="progress" min="0" max="100" value="0">
                    </div>
                    <div class="form-group">
                        <label class="form-label">任务颜色</label>
                        <div class="color-picker">
                            <div class="color-option" data-color="#3498db" style="background: #3498db;"></div>
                            <div class="color-option" data-color="#e74c3c" style="background: #e74c3c;"></div>
                            <div class="color-option" data-color="#27ae60" style="background: #27ae60;"></div>
                            <div class="color-option" data-color="#f39c12" style="background: #f39c12;"></div>
                            <div class="color-option" data-color="#9b59b6" style="background: #9b59b6;"></div>
                            <div class="color-option" data-color="#1abc9c" style="background: #1abc9c;"></div>
                        </div>
                        <input type="hidden" id="task-color" name="color" value="#3498db">
                    </div>
                </div>
                
                <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
                    <button type="button" class="btn btn-secondary" onclick="taskModal.hide()">取消</button>
                    <button type="button" class="btn btn-danger" id="delete-task-btn" onclick="deleteCurrentTask()" style="display: none;">删除</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 时间范围设置模态框 -->
    <div id="time-range-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">设置时间范围</h3>
                <button class="close" onclick="hideTimeRangeModal()">&times;</button>
            </div>
            <form id="time-range-form">
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label" for="range-start-date">开始日期</label>
                        <input type="date" class="form-control" id="range-start-date" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="range-end-date">结束日期</label>
                        <input type="date" class="form-control" id="range-end-date" required>
                    </div>
                </div>
                
                <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
                    <button type="button" class="btn btn-secondary" onclick="hideTimeRangeModal()">取消</button>
                    <button type="submit" class="btn btn-primary">应用</button>
                </div>
            </form>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="assets/js/gantt.js"></script>
    <script src="assets/js/drag-handler.js"></script>
    <script>
        // 全局变量
        let ganttChart;
        let taskModal;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        function initializeApp() {
            // 初始化甘特图
            ganttChart = new GanttChart('gantt-chart', {
                startDate: new Date(),
                endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30天
                workHoursStart: <?php echo $config['gantt']['work_hours_start']; ?>,
                workHoursEnd: <?php echo $config['gantt']['work_hours_end']; ?>
            });

            // 初始化任务模态框
            taskModal = new TaskModal();

            // 更新当前时间显示
            updateCurrentTime();
            setInterval(updateCurrentTime, 1000);

            // 设置默认时间范围
            setDefaultTimeRange();
        }

        function updateCurrentTime() {
            const now = new Date();
            const timeStr = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('current-time').textContent = timeStr;
        }

        function setDefaultTimeRange() {
            const today = new Date();
            const startDate = new Date(today);
            startDate.setDate(today.getDate() - 7); // 7天前

            const endDate = new Date(today);
            endDate.setDate(today.getDate() + 30); // 30天后

            document.getElementById('range-start-date').value = startDate.toISOString().split('T')[0];
            document.getElementById('range-end-date').value = endDate.toISOString().split('T')[0];
        }

        // 任务模态框类
        class TaskModal {
            constructor() {
                this.modal = document.getElementById('task-modal');
                this.form = document.getElementById('task-form');
                this.currentTask = null;

                this.bindEvents();
                this.initColorPicker();
            }

            bindEvents() {
                // 表单提交
                this.form.addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.saveTask();
                });

                // 模态框点击外部关闭
                this.modal.addEventListener('click', (e) => {
                    if (e.target === this.modal) {
                        this.hide();
                    }
                });

                // ESC键关闭
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'Escape' && this.modal.classList.contains('show')) {
                        this.hide();
                    }
                });
            }

            initColorPicker() {
                const colorOptions = document.querySelectorAll('.color-option');
                const colorInput = document.getElementById('task-color');

                colorOptions.forEach(option => {
                    option.addEventListener('click', () => {
                        // 移除之前的选择
                        colorOptions.forEach(opt => opt.classList.remove('selected'));

                        // 选择当前颜色
                        option.classList.add('selected');
                        colorInput.value = option.dataset.color;
                    });
                });

                // 默认选择第一个颜色
                if (colorOptions.length > 0) {
                    colorOptions[0].classList.add('selected');
                }
            }

            show(task = null) {
                this.currentTask = task;

                if (task) {
                    // 编辑模式
                    document.getElementById('modal-title').textContent = '编辑任务';
                    document.getElementById('delete-task-btn').style.display = 'block';
                    this.fillForm(task);
                } else {
                    // 新建模式
                    document.getElementById('modal-title').textContent = '新建任务';
                    document.getElementById('delete-task-btn').style.display = 'none';
                    this.resetForm();
                }

                this.modal.classList.add('show');
            }

            hide() {
                this.modal.classList.remove('show');
                this.currentTask = null;
            }

            fillForm(task) {
                document.getElementById('task-id').value = task.id;
                document.getElementById('task-name').value = task.name;
                document.getElementById('task-description').value = task.description || '';
                document.getElementById('task-progress').value = task.progress || 0;

                // 格式化时间
                const startTime = new Date(task.start_time);
                const endTime = new Date(task.end_time);

                document.getElementById('task-start-time').value = this.formatDateTimeLocal(startTime);
                document.getElementById('task-end-time').value = this.formatDateTimeLocal(endTime);

                // 设置颜色
                const colorOptions = document.querySelectorAll('.color-option');
                colorOptions.forEach(option => {
                    option.classList.remove('selected');
                    if (option.dataset.color === task.color) {
                        option.classList.add('selected');
                    }
                });
                document.getElementById('task-color').value = task.color || '#3498db';
            }

            resetForm() {
                this.form.reset();

                // 设置默认时间
                const now = new Date();
                const startTime = new Date(now);
                startTime.setMinutes(0, 0, 0); // 对齐到小时

                const endTime = new Date(startTime);
                endTime.setHours(startTime.getHours() + 8); // 默认8小时

                document.getElementById('task-start-time').value = this.formatDateTimeLocal(startTime);
                document.getElementById('task-end-time').value = this.formatDateTimeLocal(endTime);

                // 重置颜色选择
                const colorOptions = document.querySelectorAll('.color-option');
                colorOptions.forEach(option => option.classList.remove('selected'));
                if (colorOptions.length > 0) {
                    colorOptions[0].classList.add('selected');
                }
                document.getElementById('task-color').value = '#3498db';
            }

            formatDateTimeLocal(date) {
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                const hour = String(date.getHours()).padStart(2, '0');
                const minute = String(date.getMinutes()).padStart(2, '0');

                return `${year}-${month}-${day}T${hour}:${minute}`;
            }

            async saveTask() {
                const formData = new FormData(this.form);
                const taskData = {};

                for (let [key, value] of formData.entries()) {
                    taskData[key] = value;
                }

                // 验证时间
                const startTime = new Date(taskData.start_time);
                const endTime = new Date(taskData.end_time);

                if (endTime <= startTime) {
                    alert('结束时间必须晚于开始时间');
                    return;
                }

                // 格式化时间为数据库格式
                taskData.start_time = this.formatDateTime(startTime);
                taskData.end_time = this.formatDateTime(endTime);

                let success;
                if (this.currentTask) {
                    // 更新任务
                    success = await ganttChart.updateTask(this.currentTask.id, taskData);
                } else {
                    // 创建任务
                    success = await ganttChart.createTask(taskData);
                }

                if (success) {
                    this.hide();
                }
            }

            formatDateTime(date) {
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                const hour = String(date.getHours()).padStart(2, '0');
                const minute = String(date.getMinutes()).padStart(2, '0');
                const second = String(date.getSeconds()).padStart(2, '0');

                return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
            }
        }

        // 删除当前任务
        async function deleteCurrentTask() {
            if (taskModal.currentTask) {
                const success = await ganttChart.deleteTask(taskModal.currentTask.id);
                if (success) {
                    taskModal.hide();
                }
            }
        }

        // 时间范围设置
        function showTimeRangeModal() {
            document.getElementById('time-range-modal').classList.add('show');
        }

        function hideTimeRangeModal() {
            document.getElementById('time-range-modal').classList.remove('show');
        }

        // 时间范围表单提交
        document.getElementById('time-range-form').addEventListener('submit', function(e) {
            e.preventDefault();

            const startDate = new Date(document.getElementById('range-start-date').value);
            const endDate = new Date(document.getElementById('range-end-date').value);

            if (endDate <= startDate) {
                alert('结束日期必须晚于开始日期');
                return;
            }

            ganttChart.setTimeRange(startDate, endDate);
            hideTimeRangeModal();
        });

        // 导出数据功能
        function exportData() {
            const tasks = ganttChart.tasks;
            const dataStr = JSON.stringify(tasks, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `gantt-tasks-${new Date().toISOString().split('T')[0]}.json`;
            link.click();
        }

        // 模态框外部点击关闭
        document.getElementById('time-range-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideTimeRangeModal();
            }
        });
    </script>
</body>
</html>
