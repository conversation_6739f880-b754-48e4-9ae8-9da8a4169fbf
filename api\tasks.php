<?php
/**
 * 任务管理API接口
 * 处理任务的CRUD操作
 */

header('Content-Type: application/json; charset=utf-8');

require_once __DIR__ . '/database.php';

class TaskAPI {
    private $db;
    private $config;

    public function __construct() {
        $this->db = Database::getInstance();
        $this->config = require __DIR__ . '/../config/config.php';
    }

    public function handleRequest() {
        $method = $_SERVER['REQUEST_METHOD'];
        $action = $_GET['action'] ?? '';

        try {
            switch ($method) {
                case 'GET':
                    $this->handleGet($action);
                    break;
                case 'POST':
                    $this->handlePost($action);
                    break;
                case 'PUT':
                    $this->handlePut($action);
                    break;
                case 'DELETE':
                    $this->handleDelete($action);
                    break;
                default:
                    $this->sendError('不支持的请求方法', 405);
            }
        } catch (Exception $e) {
            $this->sendError($e->getMessage(), 500);
        }
    }

    private function handleGet($action) {
        switch ($action) {
            case 'list':
                $this->getTasks();
                break;
            case 'get':
                $id = $_GET['id'] ?? null;
                if (!$id) {
                    $this->sendError('缺少任务ID', 400);
                    return;
                }
                $this->getTask($id);
                break;
            default:
                $this->getTasks();
        }
    }

    private function handlePost($action) {
        $data = json_decode(file_get_contents('php://input'), true);
        
        switch ($action) {
            case 'create':
                $this->createTask($data);
                break;
            default:
                $this->sendError('无效的操作', 400);
        }
    }

    private function handlePut($action) {
        $data = json_decode(file_get_contents('php://input'), true);
        
        switch ($action) {
            case 'update':
                $id = $_GET['id'] ?? $data['id'] ?? null;
                if (!$id) {
                    $this->sendError('缺少任务ID', 400);
                    return;
                }
                $this->updateTask($id, $data);
                break;
            case 'move':
                $this->moveTask($data);
                break;
            default:
                $this->sendError('无效的操作', 400);
        }
    }

    private function handleDelete($action) {
        $id = $_GET['id'] ?? null;
        if (!$id) {
            $this->sendError('缺少任务ID', 400);
            return;
        }
        
        switch ($action) {
            case 'delete':
                $this->deleteTask($id);
                break;
            default:
                $this->sendError('无效的操作', 400);
        }
    }

    private function getTasks() {
        $sql = "
            SELECT 
                t.*,
                GROUP_CONCAT(td.depends_on_id) as dependencies
            FROM tasks t
            LEFT JOIN task_dependencies td ON t.id = td.task_id
            GROUP BY t.id
            ORDER BY t.sort_order, t.start_time
        ";
        
        $stmt = $this->db->query($sql);
        $tasks = $stmt->fetchAll();
        
        // 处理依赖关系
        foreach ($tasks as &$task) {
            $task['dependencies'] = $task['dependencies'] ? 
                array_map('intval', explode(',', $task['dependencies'])) : [];
            $task['progress'] = (int)$task['progress'];
            $task['id'] = (int)$task['id'];
            $task['parent_id'] = $task['parent_id'] ? (int)$task['parent_id'] : null;
        }
        
        $this->sendSuccess($tasks);
    }

    private function getTask($id) {
        $sql = "SELECT * FROM tasks WHERE id = ?";
        $stmt = $this->db->query($sql, [$id]);
        $task = $stmt->fetch();
        
        if (!$task) {
            $this->sendError('任务不存在', 404);
            return;
        }
        
        // 获取依赖关系
        $sql = "SELECT depends_on_id FROM task_dependencies WHERE task_id = ?";
        $stmt = $this->db->query($sql, [$id]);
        $dependencies = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $task['dependencies'] = array_map('intval', $dependencies);
        $task['progress'] = (int)$task['progress'];
        $task['id'] = (int)$task['id'];
        $task['parent_id'] = $task['parent_id'] ? (int)$task['parent_id'] : null;
        
        $this->sendSuccess($task);
    }

    private function createTask($data) {
        $required = ['name', 'start_time', 'end_time'];
        foreach ($required as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                $this->sendError("缺少必填字段: {$field}", 400);
                return;
            }
        }

        $sql = "
            INSERT INTO tasks (name, description, start_time, end_time, progress, color, parent_id, sort_order)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ";
        
        $params = [
            $data['name'],
            $data['description'] ?? '',
            $data['start_time'],
            $data['end_time'],
            $data['progress'] ?? 0,
            $data['color'] ?? '#3498db',
            $data['parent_id'] ?? null,
            $data['sort_order'] ?? 0
        ];
        
        $this->db->beginTransaction();
        try {
            $this->db->query($sql, $params);
            $taskId = $this->db->lastInsertId();
            
            // 处理依赖关系
            if (!empty($data['dependencies'])) {
                $this->saveDependencies($taskId, $data['dependencies']);
            }
            
            $this->db->commit();
            $this->sendSuccess(['id' => (int)$taskId, 'message' => '任务创建成功']);
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }

    private function updateTask($id, $data) {
        // 检查任务是否存在
        $sql = "SELECT id FROM tasks WHERE id = ?";
        $stmt = $this->db->query($sql, [$id]);
        if (!$stmt->fetch()) {
            $this->sendError('任务不存在', 404);
            return;
        }

        $updateFields = [];
        $params = [];
        
        $allowedFields = ['name', 'description', 'start_time', 'end_time', 'progress', 'color', 'parent_id', 'sort_order'];
        
        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $updateFields[] = "{$field} = ?";
                $params[] = $data[$field];
            }
        }
        
        if (empty($updateFields)) {
            $this->sendError('没有要更新的字段', 400);
            return;
        }
        
        $updateFields[] = "updated_at = CURRENT_TIMESTAMP";
        $params[] = $id;
        
        $sql = "UPDATE tasks SET " . implode(', ', $updateFields) . " WHERE id = ?";
        
        $this->db->beginTransaction();
        try {
            $this->db->query($sql, $params);
            
            // 更新依赖关系
            if (isset($data['dependencies'])) {
                // 删除现有依赖
                $this->db->query("DELETE FROM task_dependencies WHERE task_id = ?", [$id]);
                // 添加新依赖
                if (!empty($data['dependencies'])) {
                    $this->saveDependencies($id, $data['dependencies']);
                }
            }
            
            $this->db->commit();
            $this->sendSuccess(['message' => '任务更新成功']);
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }

    private function moveTask($data) {
        $id = $data['id'] ?? null;
        $startTime = $data['start_time'] ?? null;
        $endTime = $data['end_time'] ?? null;
        
        if (!$id || !$startTime || !$endTime) {
            $this->sendError('缺少必要参数', 400);
            return;
        }
        
        $sql = "UPDATE tasks SET start_time = ?, end_time = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
        $this->db->query($sql, [$startTime, $endTime, $id]);
        
        $this->sendSuccess(['message' => '任务移动成功']);
    }

    private function deleteTask($id) {
        // 检查任务是否存在
        $sql = "SELECT id FROM tasks WHERE id = ?";
        $stmt = $this->db->query($sql, [$id]);
        if (!$stmt->fetch()) {
            $this->sendError('任务不存在', 404);
            return;
        }
        
        $this->db->beginTransaction();
        try {
            // 删除依赖关系
            $this->db->query("DELETE FROM task_dependencies WHERE task_id = ? OR depends_on_id = ?", [$id, $id]);
            
            // 删除任务
            $this->db->query("DELETE FROM tasks WHERE id = ?", [$id]);
            
            $this->db->commit();
            $this->sendSuccess(['message' => '任务删除成功']);
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }

    private function saveDependencies($taskId, $dependencies) {
        if (empty($dependencies)) return;
        
        $sql = "INSERT INTO task_dependencies (task_id, depends_on_id) VALUES (?, ?)";
        foreach ($dependencies as $depId) {
            $this->db->query($sql, [$taskId, $depId]);
        }
    }

    private function sendSuccess($data) {
        echo json_encode([
            'success' => true,
            'data' => $data,
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_UNESCAPED_UNICODE);
    }

    private function sendError($message, $code = 400) {
        http_response_code($code);
        echo json_encode([
            'success' => false,
            'error' => $message,
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_UNESCAPED_UNICODE);
    }
}

// 处理请求
$api = new TaskAPI();
$api->handleRequest();
