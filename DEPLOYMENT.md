# 甘特图管理系统 - 部署指南

本系统支持三种不同的部署环境，每种环境使用独立的数据库配置。

## 🌍 环境说明

### 1. 生产环境 (Production)
- **用途**: 线上正式使用
- **数据库**: mysql320.phy.heteml.lan (_yuzong)
- **配置文件**: `config/config.production.php`
- **安装脚本**: `install-production.php`

### 2. Docker测试环境 (Docker)
- **用途**: 本地开发和测试
- **数据库**: Docker容器内的MySQL (gantt_db)
- **配置文件**: `config/config.docker.php`
- **安装脚本**: `install.php`

### 3. 本地开发环境 (Local)
- **用途**: 本地开发（可选）
- **数据库**: 本地MySQL数据库
- **配置文件**: `.env.local`
- **安装脚本**: 通用安装脚本

## 🚀 部署步骤

### 生产环境部署

1. **上传文件到服务器**
   ```bash
   # 通过FTP上传所有文件到Web目录
   ```

2. **运行生产环境安装**
   ```
   访问: https://your-domain.com/install-production.php
   ```

3. **安全设置**
   ```bash
   # 删除安装文件
   rm install-production.php
   rm install.php
   ```

### Docker测试环境部署

1. **启动Docker环境**
   ```bash
   # Windows
   start-docker.bat
   
   # Linux/Mac
   ./start-docker.sh
   ```

2. **访问测试环境**
   ```
   甘特图应用: http://localhost:8080
   phpMyAdmin: http://localhost:8081
   ```

3. **运行安装脚本**
   ```
   访问: http://localhost:8080/install.php
   ```

### 本地开发环境部署

1. **创建本地配置**
   ```bash
   cp .env.local.example .env.local
   # 编辑 .env.local 设置本地数据库信息
   ```

2. **配置本地Web服务器**
   ```bash
   # 使用XAMPP、WAMP或其他本地服务器
   ```

## 📁 文件结构

```
gantt-app/
├── 🌐 生产环境文件
│   ├── index.php                    # 主应用
│   ├── install-production.php       # 生产环境安装
│   └── config/config.production.php # 生产环境配置
│
├── 🐳 Docker测试环境文件
│   ├── docker-compose.yml          # Docker配置
│   ├── Dockerfile                  # Docker镜像
│   ├── install.php                 # Docker安装脚本
│   ├── start-docker.sh/.bat        # 启动脚本
│   └── config/config.docker.php    # Docker配置
│
├── 💻 本地开发环境文件
│   ├── .env.local.example          # 本地配置模板
│   └── .env.local                  # 本地配置（需创建）
│
├── 🔧 核心文件
│   ├── config/config.php           # 主配置文件（自动选择环境）
│   ├── api/                        # API接口
│   ├── assets/                     # 静态资源
│   └── README-Docker.md            # Docker说明
```

## 🔄 环境切换

系统会自动检测运行环境：

1. **Docker环境检测**
   - 检查 `/.dockerenv` 文件
   - 检查 `$_ENV['DOCKER_ENV']` 变量

2. **本地开发环境检测**
   - 检查 `.env.local` 文件是否存在

3. **默认生产环境**
   - 如果以上都不满足，使用生产环境配置

## 🗄️ 数据库配置

### 生产环境数据库
```php
DB_HOST = 'mysql320.phy.heteml.lan'
DB_NAME = '_yuzong'
DB_USER = '_yuzong'
DB_PASS = 'gs888888'
```

### Docker测试数据库
```php
DB_HOST = 'mysql'           # Docker容器名
DB_NAME = 'gantt_db'
DB_USER = 'gantt_user'
DB_PASS = 'gantt_pass'
```

### 本地开发数据库
```ini
# .env.local 文件
DB_HOST=localhost
DB_NAME=gantt_local
DB_USER=root
DB_PASS=your_password
```

## 🔒 安全注意事项

### 生产环境
- ✅ 删除所有安装脚本
- ✅ 关闭错误显示
- ✅ 使用HTTPS
- ✅ 定期备份数据库
- ✅ 限制数据库用户权限

### 测试环境
- ⚠️ 仅用于开发测试
- ⚠️ 不要在生产服务器运行
- ⚠️ 包含调试信息

## 🧪 测试流程

1. **本地Docker测试**
   ```bash
   # 启动Docker环境
   ./start-docker.sh
   
   # 访问测试应用
   open http://localhost:8080
   
   # 测试所有功能
   ```

2. **生产环境部署**
   ```bash
   # 上传文件到生产服务器
   # 运行生产环境安装
   # 删除安装文件
   ```

## 📊 监控和维护

### 日志查看
```bash
# Docker环境
docker-compose logs -f web

# 生产环境
tail -f /var/log/apache2/error.log
```

### 数据库备份
```bash
# 生产环境备份
mysqldump -h mysql320.phy.heteml.lan -u _yuzong -p _yuzong > backup.sql

# Docker环境备份
docker exec gantt_mysql mysqldump -u gantt_user -p gantt_db > backup.sql
```

## 🆘 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查对应环境的数据库配置
   - 验证网络连接
   - 确认数据库服务状态

2. **环境检测错误**
   - 检查文件权限
   - 验证环境变量
   - 查看错误日志

3. **Docker启动失败**
   - 检查Docker服务状态
   - 验证端口占用情况
   - 查看Docker日志

### 联系支持
如果遇到问题，请提供：
- 运行环境信息
- 错误日志
- 复现步骤

---

**选择合适的环境开始部署！** 🎯
