<?php
/**
 * 生产环境专用配置文件
 * 用于线上部署的数据库配置
 */

// 生产环境数据库配置
define('DB_HOST', 'mysql320.phy.heteml.lan');
define('DB_NAME', '_yuzong');
define('DB_USER', '_yuzong');
define('DB_PASS', 'gs888888');

// 应用配置
define('APP_NAME', '甘特图管理系统');
define('APP_VERSION', '1.0.0');
define('APP_ENV', 'production');

// 时区设置
date_default_timezone_set('Asia/Shanghai');

// 生产环境错误报告设置（关闭错误显示）
error_reporting(E_ALL & ~E_NOTICE & ~E_DEPRECATED);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// CORS设置
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 应用设置
$config = [
    'app' => [
        'name' => APP_NAME,
        'version' => APP_VERSION,
        'timezone' => 'Asia/Shanghai',
        'environment' => 'production'
    ],
    'database' => [
        'host' => DB_HOST,
        'dbname' => DB_NAME,
        'username' => DB_USER,
        'password' => DB_PASS,
        'charset' => 'utf8mb4',
        'options' => [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
        ]
    ],
    'gantt' => [
        'time_precision' => 'hour',
        'default_duration' => 8,
        'work_hours_start' => 9,
        'work_hours_end' => 18,
        'date_format' => 'Y-m-d H:i:s',
        'display_format' => 'Y-m-d H:i'
    ]
];

return $config;
