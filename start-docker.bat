@echo off
chcp 65001 >nul

echo 🚀 启动甘特图应用Docker环境...

REM 检查Docker是否安装
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker未安装，请先安装Docker Desktop
    pause
    exit /b 1
)

REM 检查Docker Compose是否安装
docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker Compose未安装，请先安装Docker Compose
    pause
    exit /b 1
)

REM 停止并删除现有容器
echo 🧹 清理现有容器...
docker-compose down -v

REM 构建并启动服务
echo 🔨 构建并启动服务...
docker-compose up -d --build

REM 等待服务启动
echo ⏳ 等待服务启动...
timeout /t 10 /nobreak >nul

REM 检查服务状态
echo 📊 检查服务状态...
docker-compose ps

REM 显示访问信息
echo.
echo ✅ 甘特图应用已启动！
echo.
echo 📱 访问地址：
echo    甘特图应用: http://localhost:8080
echo    phpMyAdmin: http://localhost:8081
echo.
echo 🗄️ 数据库信息：
echo    主机: localhost:3306
echo    数据库: gantt_db
echo    用户名: gantt_user
echo    密码: gantt_pass
echo    Root密码: root123
echo.
echo 🔧 管理命令：
echo    查看日志: docker-compose logs -f
echo    停止服务: docker-compose down
echo    重启服务: docker-compose restart
echo.
echo 🎉 开始使用甘特图管理系统吧！
echo.
echo 按任意键打开浏览器...
pause >nul

REM 打开浏览器
start http://localhost:8080
