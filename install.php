<?php
/**
 * 甘特图应用安装脚本
 * 用于初始化数据库表和示例数据
 */

require_once 'api/database.php';

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>甘特图应用 - 安装</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .install-container {
            background: white;
            border-radius: 10px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            max-width: 600px;
            width: 100%;
        }
        
        .install-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .install-header h1 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .install-header p {
            color: #666;
            font-size: 1.1rem;
        }
        
        .install-step {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #3498db;
        }
        
        .install-step h3 {
            margin-top: 0;
            color: #333;
        }
        
        .install-step.success {
            border-left-color: #27ae60;
            background: #d4edda;
        }
        
        .install-step.error {
            border-left-color: #e74c3c;
            background: #f8d7da;
        }
        
        .btn {
            background: #3498db;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            text-decoration: none;
            display: inline-block;
            transition: background 0.3s ease;
        }
        
        .btn:hover {
            background: #2980b9;
        }
        
        .btn-success {
            background: #27ae60;
        }
        
        .btn-success:hover {
            background: #229954;
        }
        
        .text-center {
            text-align: center;
        }
        
        .mt-3 {
            margin-top: 20px;
        }
        
        pre {
            background: #f4f4f4;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="install-header">
            <h1>🗓️ 甘特图管理系统</h1>
            <p>安装向导</p>
        </div>

        <?php
        $installSuccess = false;
        $errorMessage = '';
        
        try {
            // 尝试连接数据库并初始化表
            $db = Database::getInstance();
            
            echo '<div class="install-step success">';
            echo '<h3>✅ 数据库连接成功</h3>';
            echo '<p>成功连接到MySQL数据库并创建了必要的数据表。</p>';
            echo '</div>';
            
            // 检查是否已有数据
            $stmt = $db->query("SELECT COUNT(*) as count FROM tasks");
            $result = $stmt->fetch();
            $taskCount = $result['count'];
            
            if ($taskCount == 0) {
                // 插入示例数据
                $sampleTasks = [
                    [
                        'name' => '项目启动',
                        'description' => '项目启动会议和需求分析',
                        'start_time' => date('Y-m-d 09:00:00'),
                        'end_time' => date('Y-m-d 17:00:00'),
                        'progress' => 100,
                        'color' => '#27ae60'
                    ],
                    [
                        'name' => '系统设计',
                        'description' => '系统架构设计和数据库设计',
                        'start_time' => date('Y-m-d 09:00:00', strtotime('+1 day')),
                        'end_time' => date('Y-m-d 17:00:00', strtotime('+3 days')),
                        'progress' => 75,
                        'color' => '#3498db'
                    ],
                    [
                        'name' => '前端开发',
                        'description' => '用户界面开发和交互功能实现',
                        'start_time' => date('Y-m-d 09:00:00', strtotime('+2 days')),
                        'end_time' => date('Y-m-d 17:00:00', strtotime('+7 days')),
                        'progress' => 50,
                        'color' => '#f39c12'
                    ],
                    [
                        'name' => '后端开发',
                        'description' => 'API接口开发和数据库操作',
                        'start_time' => date('Y-m-d 09:00:00', strtotime('+3 days')),
                        'end_time' => date('Y-m-d 17:00:00', strtotime('+8 days')),
                        'progress' => 30,
                        'color' => '#9b59b6'
                    ],
                    [
                        'name' => '测试部署',
                        'description' => '系统测试和生产环境部署',
                        'start_time' => date('Y-m-d 09:00:00', strtotime('+8 days')),
                        'end_time' => date('Y-m-d 17:00:00', strtotime('+10 days')),
                        'progress' => 0,
                        'color' => '#e74c3c'
                    ]
                ];
                
                $insertSql = "INSERT INTO tasks (name, description, start_time, end_time, progress, color) VALUES (?, ?, ?, ?, ?, ?)";
                
                foreach ($sampleTasks as $task) {
                    $db->query($insertSql, [
                        $task['name'],
                        $task['description'],
                        $task['start_time'],
                        $task['end_time'],
                        $task['progress'],
                        $task['color']
                    ]);
                }
                
                echo '<div class="install-step success">';
                echo '<h3>✅ 示例数据创建成功</h3>';
                echo '<p>已创建 ' . count($sampleTasks) . ' 个示例任务，您可以立即开始使用甘特图功能。</p>';
                echo '</div>';
            } else {
                echo '<div class="install-step">';
                echo '<h3>ℹ️ 数据库已存在数据</h3>';
                echo '<p>检测到数据库中已有 ' . $taskCount . ' 个任务，跳过示例数据创建。</p>';
                echo '</div>';
            }
            
            $installSuccess = true;
            
        } catch (Exception $e) {
            $errorMessage = $e->getMessage();
            echo '<div class="install-step error">';
            echo '<h3>❌ 安装失败</h3>';
            echo '<p>错误信息：' . htmlspecialchars($errorMessage) . '</p>';
            echo '</div>';
            
            echo '<div class="install-step">';
            echo '<h3>🔧 解决方案</h3>';
            echo '<p>请检查以下配置：</p>';
            echo '<pre>';
            echo "数据库主机: " . DB_HOST . "\n";
            echo "数据库名称: " . DB_NAME . "\n";
            echo "数据库用户: " . DB_USER . "\n";
            echo "密码: " . (DB_PASS ? '已设置' : '未设置') . "\n";
            echo '</pre>';
            echo '<p>请确保：</p>';
            echo '<ul>';
            echo '<li>数据库服务器正在运行</li>';
            echo '<li>数据库连接信息正确</li>';
            echo '<li>数据库用户有足够的权限</li>';
            echo '<li>PHP已安装PDO MySQL扩展</li>';
            echo '</ul>';
            echo '</div>';
        }
        ?>

        <?php if ($installSuccess): ?>
            <div class="install-step success">
                <h3>🎉 安装完成</h3>
                <p>甘特图管理系统已成功安装！您现在可以开始使用所有功能。</p>
                
                <div class="text-center mt-3">
                    <a href="index.php" class="btn btn-success">进入甘特图系统</a>
                </div>
            </div>
            
            <div class="install-step">
                <h3>📋 功能说明</h3>
                <ul>
                    <li><strong>任务管理</strong>：创建、编辑、删除任务</li>
                    <li><strong>时间精度</strong>：支持小时级别的时间调度</li>
                    <li><strong>拖拽编辑</strong>：直接拖拽任务条调整时间</li>
                    <li><strong>进度跟踪</strong>：实时更新任务完成进度</li>
                    <li><strong>数据导出</strong>：支持JSON格式数据导出</li>
                    <li><strong>响应式设计</strong>：支持桌面和移动设备</li>
                </ul>
            </div>
            
            <div class="install-step">
                <h3>🚀 下一步</h3>
                <p>建议您：</p>
                <ol>
                    <li>删除或重命名 <code>install.php</code> 文件以提高安全性</li>
                    <li>在生产环境中关闭PHP错误显示</li>
                    <li>定期备份数据库数据</li>
                    <li>根据需要调整时间范围和工作时间设置</li>
                </ol>
            </div>
        <?php else: ?>
            <div class="text-center mt-3">
                <button onclick="location.reload()" class="btn">重试安装</button>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>
