# 甘特图管理系统 - Docker测试环境

这是一个完整的Docker测试环境，包含PHP 8.3、MySQL 8.0和phpMyAdmin。

## 🚀 快速启动

### 方法1：使用启动脚本（推荐）

**Windows用户：**
```bash
# 双击运行
start-docker.bat
```

**Linux/Mac用户：**
```bash
# 给脚本执行权限
chmod +x start-docker.sh

# 运行脚本
./start-docker.sh
```

### 方法2：手动启动

```bash
# 构建并启动所有服务
docker-compose up -d --build

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

## 📱 访问地址

启动成功后，您可以访问：

- **甘特图应用**: http://localhost:8080
- **phpMyAdmin**: http://localhost:8081

## 🗄️ 数据库信息

- **主机**: localhost:3306
- **数据库名**: gantt_db
- **用户名**: gantt_user
- **密码**: gantt_pass
- **Root密码**: root123

## 🔧 管理命令

```bash
# 查看运行状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f web
docker-compose logs -f mysql

# 重启服务
docker-compose restart

# 停止服务
docker-compose down

# 停止服务并删除数据
docker-compose down -v

# 重新构建
docker-compose up -d --build
```

## 🧪 测试功能

启动后，您可以测试以下功能：

1. **访问主页**: http://localhost:8080
2. **创建任务**: 点击"新建任务"按钮
3. **拖拽任务**: 直接拖拽任务条调整时间
4. **编辑任务**: 双击任务条进行编辑
5. **查看数据库**: 通过phpMyAdmin查看数据变化

## 📊 示例数据

系统会自动创建以下示例任务：

- 项目启动 (已完成 100%)
- 系统设计 (进行中 75%)
- 前端开发 (进行中 50%)
- 后端开发 (进行中 30%)
- 测试部署 (未开始 0%)

## 🐛 故障排除

### 端口冲突
如果端口8080或8081被占用，修改`docker-compose.yml`中的端口映射：

```yaml
ports:
  - "8082:80"  # 改为8082端口
```

### 数据库连接失败
1. 确保MySQL容器正常运行：`docker-compose ps`
2. 查看MySQL日志：`docker-compose logs mysql`
3. 重启服务：`docker-compose restart`

### 权限问题（Linux/Mac）
```bash
# 给脚本执行权限
chmod +x start-docker.sh

# 如果需要sudo权限
sudo docker-compose up -d --build
```

## 🔄 开发模式

代码修改会实时反映到容器中，无需重新构建。

如果修改了Docker配置文件，需要重新构建：
```bash
docker-compose down
docker-compose up -d --build
```

## 📝 日志查看

```bash
# 查看所有服务日志
docker-compose logs -f

# 查看Web服务日志
docker-compose logs -f web

# 查看MySQL日志
docker-compose logs -f mysql

# 查看phpMyAdmin日志
docker-compose logs -f phpmyadmin
```

## 🧹 清理环境

```bash
# 停止并删除容器
docker-compose down

# 删除容器和数据卷
docker-compose down -v

# 删除镜像（可选）
docker rmi $(docker images -q)
```

## 📞 技术支持

如果遇到问题：

1. 检查Docker是否正常运行
2. 查看容器日志排查错误
3. 确认端口没有被占用
4. 重启Docker服务

---

**开始测试甘特图管理系统！** 🎉
