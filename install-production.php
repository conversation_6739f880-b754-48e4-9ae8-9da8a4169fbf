<?php
/**
 * 生产环境安装脚本
 * 专门用于线上数据库的初始化
 */

// 强制使用生产环境配置
require_once 'config/config.production.php';
require_once 'api/database.php';

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>甘特图应用 - 生产环境安装</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .install-container {
            background: white;
            border-radius: 10px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            max-width: 600px;
            width: 100%;
        }
        
        .install-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .install-header h1 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .install-header p {
            color: #666;
            font-size: 1.1rem;
        }
        
        .install-step {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #3498db;
        }
        
        .install-step h3 {
            margin-top: 0;
            color: #333;
        }
        
        .install-step.success {
            border-left-color: #27ae60;
            background: #d4edda;
        }
        
        .install-step.error {
            border-left-color: #e74c3c;
            background: #f8d7da;
        }
        
        .install-step.warning {
            border-left-color: #f39c12;
            background: #fff3cd;
        }
        
        .btn {
            background: #3498db;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            text-decoration: none;
            display: inline-block;
            transition: background 0.3s ease;
        }
        
        .btn:hover {
            background: #2980b9;
        }
        
        .btn-success {
            background: #27ae60;
        }
        
        .btn-success:hover {
            background: #229954;
        }
        
        .text-center {
            text-align: center;
        }
        
        .mt-3 {
            margin-top: 20px;
        }
        
        pre {
            background: #f4f4f4;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 14px;
        }
        
        .env-info {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="install-header">
            <h1>🗓️ 甘特图管理系统</h1>
            <p>生产环境安装向导</p>
        </div>

        <div class="install-step warning">
            <h3>⚠️ 生产环境安装</h3>
            <p>您正在安装到生产环境，请确认以下信息：</p>
            <div class="env-info">
                <strong>数据库配置：</strong><br>
                主机: <?php echo DB_HOST; ?><br>
                数据库: <?php echo DB_NAME; ?><br>
                用户: <?php echo DB_USER; ?><br>
                环境: 生产环境
            </div>
        </div>

        <?php
        $installSuccess = false;
        $errorMessage = '';
        
        try {
            // 尝试连接数据库并初始化表
            $db = Database::getInstance();
            
            echo '<div class="install-step success">';
            echo '<h3>✅ 生产数据库连接成功</h3>';
            echo '<p>成功连接到生产环境MySQL数据库并创建了必要的数据表。</p>';
            echo '</div>';
            
            // 检查是否已有数据
            $stmt = $db->query("SELECT COUNT(*) as count FROM tasks");
            $result = $stmt->fetch();
            $taskCount = $result['count'];
            
            if ($taskCount == 0) {
                // 插入生产环境示例数据
                $sampleTasks = [
                    [
                        'name' => '项目规划',
                        'description' => '制定项目计划和里程碑',
                        'start_time' => date('Y-m-d 09:00:00'),
                        'end_time' => date('Y-m-d 17:00:00'),
                        'progress' => 0,
                        'color' => '#3498db'
                    ],
                    [
                        'name' => '需求分析',
                        'description' => '收集和分析业务需求',
                        'start_time' => date('Y-m-d 09:00:00', strtotime('+1 day')),
                        'end_time' => date('Y-m-d 17:00:00', strtotime('+2 days')),
                        'progress' => 0,
                        'color' => '#27ae60'
                    ],
                    [
                        'name' => '系统开发',
                        'description' => '核心功能开发和实现',
                        'start_time' => date('Y-m-d 09:00:00', strtotime('+3 days')),
                        'end_time' => date('Y-m-d 17:00:00', strtotime('+10 days')),
                        'progress' => 0,
                        'color' => '#f39c12'
                    ]
                ];
                
                $insertSql = "INSERT INTO tasks (name, description, start_time, end_time, progress, color) VALUES (?, ?, ?, ?, ?, ?)";
                
                foreach ($sampleTasks as $task) {
                    $db->query($insertSql, [
                        $task['name'],
                        $task['description'],
                        $task['start_time'],
                        $task['end_time'],
                        $task['progress'],
                        $task['color']
                    ]);
                }
                
                echo '<div class="install-step success">';
                echo '<h3>✅ 生产环境数据初始化完成</h3>';
                echo '<p>已创建 ' . count($sampleTasks) . ' 个初始任务模板。</p>';
                echo '</div>';
            } else {
                echo '<div class="install-step">';
                echo '<h3>ℹ️ 数据库已存在数据</h3>';
                echo '<p>检测到生产数据库中已有 ' . $taskCount . ' 个任务，跳过示例数据创建。</p>';
                echo '</div>';
            }
            
            $installSuccess = true;
            
        } catch (Exception $e) {
            $errorMessage = $e->getMessage();
            echo '<div class="install-step error">';
            echo '<h3>❌ 生产环境安装失败</h3>';
            echo '<p>错误信息：' . htmlspecialchars($errorMessage) . '</p>';
            echo '</div>';
            
            echo '<div class="install-step">';
            echo '<h3>🔧 解决方案</h3>';
            echo '<p>请检查生产环境配置：</p>';
            echo '<pre>';
            echo "数据库主机: " . DB_HOST . "\n";
            echo "数据库名称: " . DB_NAME . "\n";
            echo "数据库用户: " . DB_USER . "\n";
            echo "环境类型: 生产环境\n";
            echo '</pre>';
            echo '<p>请确保：</p>';
            echo '<ul>';
            echo '<li>生产数据库服务器正在运行</li>';
            echo '<li>数据库连接信息正确</li>';
            echo '<li>数据库用户有足够的权限</li>';
            echo '<li>网络连接正常</li>';
            echo '</ul>';
            echo '</div>';
        }
        ?>

        <?php if ($installSuccess): ?>
            <div class="install-step success">
                <h3>🎉 生产环境安装完成</h3>
                <p>甘特图管理系统已成功安装到生产环境！</p>
                
                <div class="text-center mt-3">
                    <a href="index.php" class="btn btn-success">进入甘特图系统</a>
                </div>
            </div>
            
            <div class="install-step warning">
                <h3>🔒 安全提醒</h3>
                <p><strong>重要：</strong>安装完成后请立即：</p>
                <ol>
                    <li>删除或重命名 <code>install-production.php</code> 文件</li>
                    <li>删除或重命名 <code>install.php</code> 文件</li>
                    <li>确认生产环境已关闭错误显示</li>
                    <li>设置定期数据库备份</li>
                    <li>配置HTTPS访问</li>
                </ol>
            </div>
        <?php else: ?>
            <div class="text-center mt-3">
                <button onclick="location.reload()" class="btn">重试安装</button>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>
