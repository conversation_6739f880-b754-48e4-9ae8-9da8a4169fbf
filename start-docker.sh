#!/bin/bash

# 甘特图应用Docker启动脚本

echo "🚀 启动甘特图应用Docker环境..."

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

# 检查Docker Compose是否安装
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 停止并删除现有容器
echo "🧹 清理现有容器..."
docker-compose down -v

# 构建并启动服务
echo "🔨 构建并启动服务..."
docker-compose up -d --build

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "📊 检查服务状态..."
docker-compose ps

# 显示访问信息
echo ""
echo "✅ 甘特图应用已启动！"
echo ""
echo "📱 访问地址："
echo "   甘特图应用: http://localhost:8080"
echo "   phpMyAdmin: http://localhost:8081"
echo ""
echo "🗄️ 数据库信息："
echo "   主机: localhost:3306"
echo "   数据库: gantt_db"
echo "   用户名: gantt_user"
echo "   密码: gantt_pass"
echo "   Root密码: root123"
echo ""
echo "🔧 管理命令："
echo "   查看日志: docker-compose logs -f"
echo "   停止服务: docker-compose down"
echo "   重启服务: docker-compose restart"
echo ""
echo "🎉 开始使用甘特图管理系统吧！"
