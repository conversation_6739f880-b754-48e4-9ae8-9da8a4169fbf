<?php
/**
 * Docker测试环境专用配置文件
 * 用于本地Docker容器测试
 */

// Docker环境数据库配置
define('DB_HOST', 'mysql');
define('DB_NAME', 'gantt_db');
define('DB_USER', 'gantt_user');
define('DB_PASS', 'gantt_pass');

// 应用配置
define('APP_NAME', '甘特图管理系统 (Docker测试)');
define('APP_VERSION', '1.0.0-docker');
define('APP_ENV', 'docker');

// 时区设置
date_default_timezone_set('Asia/Shanghai');

// 开发环境错误报告设置（显示所有错误）
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// CORS设置
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 应用设置
$config = [
    'app' => [
        'name' => APP_NAME,
        'version' => APP_VERSION,
        'timezone' => 'Asia/Shanghai',
        'environment' => 'docker'
    ],
    'database' => [
        'host' => DB_HOST,
        'dbname' => DB_NAME,
        'username' => DB_USER,
        'password' => DB_PASS,
        'charset' => 'utf8mb4',
        'options' => [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
        ]
    ],
    'gantt' => [
        'time_precision' => 'hour',
        'default_duration' => 8,
        'work_hours_start' => 9,
        'work_hours_end' => 18,
        'date_format' => 'Y-m-d H:i:s',
        'display_format' => 'Y-m-d H:i'
    ]
];

return $config;
