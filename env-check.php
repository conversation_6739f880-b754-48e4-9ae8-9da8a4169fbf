<?php
/**
 * 环境检测页面
 * 帮助用户了解当前运行环境和配置
 */

require_once 'config/config.php';
$config = require 'config/config.php';

// 环境检测
$isDocker = file_exists('/.dockerenv') || isset($_ENV['DOCKER_ENV']);
$isLocal = file_exists(__DIR__ . '/.env.local');

if ($isDocker) {
    $environment = 'Docker测试环境';
    $envClass = 'docker';
} elseif ($isLocal) {
    $environment = '本地开发环境';
    $envClass = 'local';
} else {
    $environment = '生产环境';
    $envClass = 'production';
}

// 数据库连接测试
$dbStatus = 'unknown';
$dbMessage = '';
try {
    $db = Database::getInstance();
    $dbStatus = 'success';
    $dbMessage = '数据库连接正常';
} catch (Exception $e) {
    $dbStatus = 'error';
    $dbMessage = '数据库连接失败: ' . $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>环境检测 - 甘特图管理系统</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .env-badge {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            color: white;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .env-badge.docker {
            background: #0db7ed;
        }
        
        .env-badge.local {
            background: #28a745;
        }
        
        .env-badge.production {
            background: #dc3545;
        }
        
        .info-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #3498db;
        }
        
        .info-section h3 {
            margin-top: 0;
            color: #333;
        }
        
        .info-section.success {
            border-left-color: #27ae60;
            background: #d4edda;
        }
        
        .info-section.error {
            border-left-color: #e74c3c;
            background: #f8d7da;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .info-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
        }
        
        .info-card h4 {
            margin-top: 0;
            color: #495057;
        }
        
        .info-card .value {
            font-family: monospace;
            background: #f8f9fa;
            padding: 5px 8px;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .btn {
            background: #3498db;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
            transition: background 0.3s ease;
        }
        
        .btn:hover {
            background: #2980b9;
        }
        
        .btn-success {
            background: #27ae60;
        }
        
        .btn-success:hover {
            background: #229954;
        }
        
        .text-center {
            text-align: center;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success {
            background: #27ae60;
        }
        
        .status-error {
            background: #e74c3c;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 环境检测</h1>
            <div class="env-badge <?php echo $envClass; ?>">
                <?php echo $environment; ?>
            </div>
        </div>

        <!-- 数据库状态 -->
        <div class="info-section <?php echo $dbStatus; ?>">
            <h3>
                <span class="status-indicator status-<?php echo $dbStatus; ?>"></span>
                数据库连接状态
            </h3>
            <p><?php echo $dbMessage; ?></p>
        </div>

        <!-- 环境信息 -->
        <div class="info-section">
            <h3>📊 环境信息</h3>
            <div class="info-grid">
                <div class="info-card">
                    <h4>运行环境</h4>
                    <div class="value"><?php echo $environment; ?></div>
                </div>
                
                <div class="info-card">
                    <h4>PHP版本</h4>
                    <div class="value"><?php echo PHP_VERSION; ?></div>
                </div>
                
                <div class="info-card">
                    <h4>应用版本</h4>
                    <div class="value"><?php echo $config['app']['version']; ?></div>
                </div>
                
                <div class="info-card">
                    <h4>时区设置</h4>
                    <div class="value"><?php echo date_default_timezone_get(); ?></div>
                </div>
            </div>
        </div>

        <!-- 数据库配置 -->
        <div class="info-section">
            <h3>🗄️ 数据库配置</h3>
            <div class="info-grid">
                <div class="info-card">
                    <h4>数据库主机</h4>
                    <div class="value"><?php echo $config['database']['host']; ?></div>
                </div>
                
                <div class="info-card">
                    <h4>数据库名称</h4>
                    <div class="value"><?php echo $config['database']['dbname']; ?></div>
                </div>
                
                <div class="info-card">
                    <h4>数据库用户</h4>
                    <div class="value"><?php echo $config['database']['username']; ?></div>
                </div>
                
                <div class="info-card">
                    <h4>字符集</h4>
                    <div class="value"><?php echo $config['database']['charset']; ?></div>
                </div>
            </div>
        </div>

        <!-- 系统信息 -->
        <div class="info-section">
            <h3>⚙️ 系统信息</h3>
            <div class="info-grid">
                <div class="info-card">
                    <h4>操作系统</h4>
                    <div class="value"><?php echo PHP_OS; ?></div>
                </div>
                
                <div class="info-card">
                    <h4>Web服务器</h4>
                    <div class="value"><?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></div>
                </div>
                
                <div class="info-card">
                    <h4>当前时间</h4>
                    <div class="value"><?php echo date('Y-m-d H:i:s'); ?></div>
                </div>
                
                <div class="info-card">
                    <h4>内存限制</h4>
                    <div class="value"><?php echo ini_get('memory_limit'); ?></div>
                </div>
            </div>
        </div>

        <!-- 环境特定信息 -->
        <?php if ($isDocker): ?>
        <div class="info-section" style="border-left-color: #0db7ed; background: #e3f2fd;">
            <h3>🐳 Docker环境信息</h3>
            <p>您正在Docker容器中运行甘特图系统。</p>
            <ul>
                <li>数据库运行在独立的MySQL容器中</li>
                <li>所有数据与生产环境完全隔离</li>
                <li>适合开发和测试使用</li>
                <li>可通过 phpMyAdmin 管理数据库</li>
            </ul>
        </div>
        <?php elseif ($isLocal): ?>
        <div class="info-section" style="border-left-color: #28a745; background: #d1ecf1;">
            <h3>💻 本地开发环境</h3>
            <p>您正在本地开发环境中运行。</p>
            <ul>
                <li>使用本地数据库配置</li>
                <li>配置文件: .env.local</li>
                <li>适合本地开发使用</li>
            </ul>
        </div>
        <?php else: ?>
        <div class="info-section" style="border-left-color: #dc3545; background: #f8d7da;">
            <h3>🌐 生产环境</h3>
            <p>您正在生产环境中运行甘特图系统。</p>
            <ul>
                <li>连接到线上数据库</li>
                <li>错误显示已关闭</li>
                <li>请确保已删除安装文件</li>
                <li>建议定期备份数据</li>
            </ul>
        </div>
        <?php endif; ?>

        <!-- 操作按钮 -->
        <div class="text-center" style="margin-top: 30px;">
            <a href="index.php" class="btn btn-success">进入甘特图系统</a>
            
            <?php if ($isDocker): ?>
                <a href="install.php" class="btn">Docker安装脚本</a>
            <?php else: ?>
                <a href="install-production.php" class="btn">生产环境安装</a>
            <?php endif; ?>
            
            <button onclick="location.reload()" class="btn">刷新检测</button>
        </div>
    </div>
</body>
</html>
