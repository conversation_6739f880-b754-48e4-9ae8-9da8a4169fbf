# 甘特图管理系统

一个基于PHP 8.3.21和MySQL的专业甘特图Web应用程序，支持小时级精度的项目时间管理。

## 🌟 主要特性

- **精确时间管理**：支持小时级别的任务调度和显示
- **直观拖拽操作**：任务条可直接拖拽调整开始和结束时间
- **实时数据保存**：所有操作实时保存到MySQL数据库
- **响应式设计**：完美支持桌面和移动设备
- **现代化界面**：美观的用户界面和流畅的交互体验
- **数据导出功能**：支持JSON格式的数据导出
- **无依赖部署**：纯PHP+HTML+CSS+JavaScript，无需Node.js

## 📋 系统要求

### 服务器环境
- **PHP版本**：8.3.21 或更高版本
- **数据库**：MySQL 5.7+ 或 MariaDB 10.2+
- **Web服务器**：Apache 2.4+ 或 Nginx 1.18+
- **PHP扩展**：PDO、PDO_MySQL

### 浏览器支持
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 🚀 快速安装

### 1. 下载和上传文件

将所有项目文件通过FTP上传到您的Web服务器目录：

```
your-domain.com/gantt-app/
├── index.php              # 主页面
├── install.php            # 安装脚本
├── config/
│   └── config.php         # 配置文件
├── api/
│   ├── database.php       # 数据库类
│   └── tasks.php          # API接口
├── assets/
│   ├── css/
│   │   └── gantt.css      # 样式文件
│   └── js/
│       ├── gantt.js       # 甘特图核心
│       └── drag-handler.js # 拖拽处理
└── README.md              # 说明文档
```

### 2. 配置数据库连接

编辑 `config/config.php` 文件，修改数据库连接信息：

```php
// 数据库配置
define('DB_HOST', 'your-mysql-host');
define('DB_NAME', 'your-database-name');
define('DB_USER', 'your-username');
define('DB_PASS', 'your-password');
```

### 3. 运行安装脚本

在浏览器中访问：`http://your-domain.com/gantt-app/install.php`

安装脚本将自动：
- 测试数据库连接
- 创建必要的数据表
- 插入示例数据
- 验证安装结果

### 4. 开始使用

安装完成后，访问：`http://your-domain.com/gantt-app/index.php`

## 📊 数据库结构

### tasks 表
```sql
CREATE TABLE tasks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    start_time DATETIME NOT NULL,
    end_time DATETIME NOT NULL,
    progress INT DEFAULT 0,
    color VARCHAR(7) DEFAULT '#3498db',
    parent_id INT DEFAULT NULL,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### task_dependencies 表
```sql
CREATE TABLE task_dependencies (
    id INT AUTO_INCREMENT PRIMARY KEY,
    task_id INT NOT NULL,
    depends_on_id INT NOT NULL,
    dependency_type VARCHAR(50) DEFAULT 'finish_to_start',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 🎯 核心功能

### 任务管理
- ✅ 创建新任务
- ✅ 编辑任务信息
- ✅ 删除任务
- ✅ 设置任务进度
- ✅ 自定义任务颜色

### 时间操作
- ✅ 小时级精度显示
- ✅ 拖拽调整任务时间
- ✅ 自定义时间范围
- ✅ 当前时间指示器

### 数据功能
- ✅ 实时数据保存
- ✅ JSON格式导出
- ✅ 数据验证和错误处理

### 用户体验
- ✅ 响应式设计
- ✅ 触摸设备支持
- ✅ 键盘快捷键
- ✅ 加载状态提示

## 🔧 配置选项

在 `config/config.php` 中可以调整以下设置：

```php
'gantt' => [
    'time_precision' => 'hour',     // 时间精度
    'default_duration' => 8,        // 默认任务持续时间（小时）
    'work_hours_start' => 9,        // 工作时间开始
    'work_hours_end' => 18,         // 工作时间结束
    'date_format' => 'Y-m-d H:i:s', // 数据库日期格式
    'display_format' => 'Y-m-d H:i' // 显示日期格式
]
```

## 🛠️ API接口

### 获取任务列表
```
GET /api/tasks.php?action=list
```

### 创建任务
```
POST /api/tasks.php?action=create
Content-Type: application/json

{
    "name": "任务名称",
    "description": "任务描述",
    "start_time": "2024-01-01 09:00:00",
    "end_time": "2024-01-01 17:00:00",
    "progress": 0,
    "color": "#3498db"
}
```

### 更新任务
```
PUT /api/tasks.php?action=update&id=1
Content-Type: application/json

{
    "name": "更新的任务名称",
    "progress": 50
}
```

### 移动任务
```
PUT /api/tasks.php?action=move
Content-Type: application/json

{
    "id": 1,
    "start_time": "2024-01-02 09:00:00",
    "end_time": "2024-01-02 17:00:00"
}
```

### 删除任务
```
DELETE /api/tasks.php?action=delete&id=1
```

## 🔒 安全建议

1. **删除安装文件**：安装完成后删除 `install.php`
2. **关闭错误显示**：生产环境中设置 `display_errors = 0`
3. **数据库权限**：使用最小权限的数据库用户
4. **HTTPS部署**：在生产环境中使用HTTPS
5. **定期备份**：定期备份数据库数据

## 🐛 故障排除

### 常见问题

**问题1：数据库连接失败**
- 检查数据库服务器是否运行
- 验证连接信息是否正确
- 确认PHP PDO MySQL扩展已安装

**问题2：任务拖拽不工作**
- 检查浏览器JavaScript是否启用
- 确认没有JavaScript错误
- 验证CSS文件是否正确加载

**问题3：时间显示不正确**
- 检查服务器时区设置
- 验证PHP时区配置
- 确认数据库时区设置

### 调试模式

在 `config/config.php` 中启用调试：

```php
// 错误报告设置（开发环境）
error_reporting(E_ALL);
ini_set('display_errors', 1);
```

## 📞 技术支持

如果您在使用过程中遇到问题，请检查：

1. **系统要求**：确保满足所有系统要求
2. **错误日志**：查看PHP和Web服务器错误日志
3. **浏览器控制台**：检查JavaScript错误
4. **数据库连接**：验证数据库配置和权限

## 📄 许可证

本项目采用MIT许可证，详情请参阅LICENSE文件。

## 🔄 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 基础甘特图功能
- 任务CRUD操作
- 拖拽时间调整
- 响应式设计
- MySQL数据库支持

---

**享受使用甘特图管理系统！** 🎉
