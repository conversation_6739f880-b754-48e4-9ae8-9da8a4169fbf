<?php
/**
 * 甘特图应用配置文件
 * PHP 8.3.21 兼容
 */

// 数据库配置
define('DB_PATH', __DIR__ . '/../data/gantt.db');

// 应用配置
define('APP_NAME', '甘特图管理系统');
define('APP_VERSION', '1.0.0');

// 时区设置
date_default_timezone_set('Asia/Shanghai');

// 错误报告设置（生产环境建议关闭）
error_reporting(E_ALL);
ini_set('display_errors', 1);

// CORS设置（如果需要跨域访问）
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 确保数据目录存在
$dataDir = __DIR__ . '/../data';
if (!is_dir($dataDir)) {
    mkdir($dataDir, 0755, true);
}

// 应用设置
$config = [
    'app' => [
        'name' => APP_NAME,
        'version' => APP_VERSION,
        'timezone' => 'Asia/Shanghai'
    ],
    'database' => [
        'path' => DB_PATH,
        'options' => [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    ],
    'gantt' => [
        'time_precision' => 'hour', // 时间精度：hour
        'default_duration' => 8,    // 默认任务持续时间（小时）
        'work_hours_start' => 9,    // 工作时间开始
        'work_hours_end' => 18,     // 工作时间结束
        'date_format' => 'Y-m-d H:i:s',
        'display_format' => 'Y-m-d H:i'
    ]
];

return $config;
