<?php
/**
 * 甘特图应用配置文件
 * PHP 8.3.21 兼容 - MySQL版本
 */

// 环境检测和数据库配置
$isDocker = file_exists('/.dockerenv') || isset($_ENV['DOCKER_ENV']);
$isLocal = file_exists(__DIR__ . '/../.env.local');

if ($isDocker) {
    // Docker测试环境
    define('DB_HOST', 'mysql');
    define('DB_NAME', 'gantt_db');
    define('DB_USER', 'gantt_user');
    define('DB_PASS', 'gantt_pass');
} elseif ($isLocal) {
    // 本地开发环境
    $localConfig = parse_ini_file(__DIR__ . '/../.env.local');
    define('DB_HOST', $localConfig['DB_HOST']);
    define('DB_NAME', $localConfig['DB_NAME']);
    define('DB_USER', $localConfig['DB_USER']);
    define('DB_PASS', $localConfig['DB_PASS']);
} else {
    // 生产环境 - 您的线上数据库
    define('DB_HOST', 'mysql320.phy.heteml.lan');
    define('DB_NAME', '_yuzong');
    define('DB_USER', '_yuzong');
    define('DB_PASS', 'gs888888');
}

// 应用配置
define('APP_NAME', '甘特图管理系统');
define('APP_VERSION', '1.0.0');

// 时区设置
date_default_timezone_set('Asia/Shanghai');

// 错误报告设置（生产环境建议关闭）
error_reporting(E_ALL);
ini_set('display_errors', 1);

// CORS设置（如果需要跨域访问）
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 根据环境加载对应的配置
if ($isDocker) {
    // Docker测试环境
    return require __DIR__ . '/config.docker.php';
} elseif ($isLocal) {
    // 本地开发环境 - 使用基础配置但从.env.local读取数据库信息
    $config = [
        'app' => [
            'name' => '甘特图管理系统 (本地开发)',
            'version' => '1.0.0-local',
            'timezone' => 'Asia/Shanghai',
            'environment' => 'local'
        ],
        'database' => [
            'host' => DB_HOST,
            'dbname' => DB_NAME,
            'username' => DB_USER,
            'password' => DB_PASS,
            'charset' => 'utf8mb4',
            'options' => [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
            ]
        ],
        'gantt' => [
            'time_precision' => 'hour',
            'default_duration' => 8,
            'work_hours_start' => 9,
            'work_hours_end' => 18,
            'date_format' => 'Y-m-d H:i:s',
            'display_format' => 'Y-m-d H:i'
        ]
    ];
    return $config;
} else {
    // 生产环境
    return require __DIR__ . '/config.production.php';
}
