<?php
/**
 * 甘特图应用配置文件
 * PHP 8.3.21 兼容 - MySQL版本
 */

// 数据库配置
define('DB_HOST', 'mysql320.phy.heteml.lan');
define('DB_NAME', '_yuzong');
define('DB_USER', '_yuzong');
define('DB_PASS', 'gs888888');

// 应用配置
define('APP_NAME', '甘特图管理系统');
define('APP_VERSION', '1.0.0');

// 时区设置
date_default_timezone_set('Asia/Shanghai');

// 错误报告设置（生产环境建议关闭）
error_reporting(E_ALL);
ini_set('display_errors', 1);

// CORS设置（如果需要跨域访问）
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 应用设置
$config = [
    'app' => [
        'name' => APP_NAME,
        'version' => APP_VERSION,
        'timezone' => 'Asia/Shanghai'
    ],
    'database' => [
        'host' => DB_HOST,
        'dbname' => DB_NAME,
        'username' => DB_USER,
        'password' => DB_PASS,
        'charset' => 'utf8mb4',
        'options' => [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
        ]
    ],
    'gantt' => [
        'time_precision' => 'hour', // 时间精度：hour
        'default_duration' => 8,    // 默认任务持续时间（小时）
        'work_hours_start' => 9,    // 工作时间开始
        'work_hours_end' => 18,     // 工作时间结束
        'date_format' => 'Y-m-d H:i:s',
        'display_format' => 'Y-m-d H:i'
    ]
];

return $config;
