/**
 * 甘特图核心JavaScript类
 * 处理甘特图的渲染、交互和数据管理
 */

class GanttChart {
    constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId);
        this.options = {
            timeScale: 'hour',
            startDate: new Date(),
            endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30天后
            workHoursStart: 9,
            workHoursEnd: 18,
            cellWidth: 60,
            rowHeight: 40,
            ...options
        };
        
        this.tasks = [];
        this.selectedTask = null;
        this.dragHandler = null;
        
        this.init();
    }

    init() {
        this.createStructure();
        this.loadTasks();
        this.bindEvents();
        this.initDragHandler();
    }

    createStructure() {
        this.container.innerHTML = `
            <div class="gantt-wrapper">
                <table class="gantt-table">
                    <thead class="gantt-header">
                        <tr id="gantt-header-row">
                            <th class="task-column">任务名称</th>
                        </tr>
                    </thead>
                    <tbody id="gantt-body">
                    </tbody>
                </table>
            </div>
        `;
        
        this.headerRow = document.getElementById('gantt-header-row');
        this.tbody = document.getElementById('gantt-body');
        
        this.generateTimeHeaders();
    }

    generateTimeHeaders() {
        const startTime = new Date(this.options.startDate);
        const endTime = new Date(this.options.endDate);
        
        // 清除现有的时间列
        const existingTimeCells = this.headerRow.querySelectorAll('.time-column');
        existingTimeCells.forEach(cell => cell.remove());
        
        let currentTime = new Date(startTime);
        
        while (currentTime <= endTime) {
            const th = document.createElement('th');
            th.className = 'time-column';
            th.textContent = this.formatTimeHeader(currentTime);
            th.dataset.time = currentTime.toISOString();
            this.headerRow.appendChild(th);
            
            // 按小时递增
            currentTime.setHours(currentTime.getHours() + 1);
        }
    }

    formatTimeHeader(date) {
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hour = String(date.getHours()).padStart(2, '0');
        
        // 如果是每天的第一个小时，显示日期
        if (date.getHours() === 0) {
            return `${month}/${day}`;
        }
        
        return `${hour}:00`;
    }

    async loadTasks() {
        try {
            this.showLoading();
            const response = await fetch('api/tasks.php?action=list');
            const result = await response.json();
            
            if (result.success) {
                this.tasks = result.data;
                this.renderTasks();
            } else {
                this.showError('加载任务失败: ' + result.error);
            }
        } catch (error) {
            this.showError('网络错误: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    renderTasks() {
        this.tbody.innerHTML = '';
        
        this.tasks.forEach(task => {
            const row = this.createTaskRow(task);
            this.tbody.appendChild(row);
        });
        
        this.updateCurrentTimeIndicator();
    }

    createTaskRow(task) {
        const row = document.createElement('tr');
        row.className = 'gantt-row';
        row.dataset.taskId = task.id;
        
        // 任务信息列
        const taskCell = document.createElement('td');
        taskCell.className = 'task-info';
        taskCell.innerHTML = `
            <div class="task-name">${this.escapeHtml(task.name)}</div>
            <div class="task-duration">${this.formatDuration(task.start_time, task.end_time)}</div>
        `;
        row.appendChild(taskCell);
        
        // 时间列
        const timeColumns = this.headerRow.querySelectorAll('.time-column');
        timeColumns.forEach(th => {
            const cell = document.createElement('td');
            cell.className = 'gantt-cell time-cell';
            cell.dataset.time = th.dataset.time;
            
            // 检查是否是周末
            const cellDate = new Date(th.dataset.time);
            if (cellDate.getDay() === 0 || cellDate.getDay() === 6) {
                cell.classList.add('weekend');
            }
            
            row.appendChild(cell);
        });
        
        // 创建任务条
        this.createTaskBar(row, task);
        
        return row;
    }

    createTaskBar(row, task) {
        const startTime = new Date(task.start_time);
        const endTime = new Date(task.end_time);
        
        const startPosition = this.getTimePosition(startTime);
        const endPosition = this.getTimePosition(endTime);
        
        if (startPosition === -1 || endPosition === -1) {
            return; // 任务不在当前时间范围内
        }
        
        const taskBar = document.createElement('div');
        taskBar.className = `task-bar ${this.getTaskColorClass(task.color)}`;
        taskBar.dataset.taskId = task.id;
        taskBar.textContent = task.name;
        
        // 设置位置和大小
        const left = startPosition * this.options.cellWidth;
        const width = Math.max((endPosition - startPosition) * this.options.cellWidth, 30);
        
        taskBar.style.left = `${left}px`;
        taskBar.style.width = `${width}px`;
        
        // 添加进度条
        if (task.progress > 0) {
            const progressBar = document.createElement('div');
            progressBar.className = 'task-progress';
            progressBar.style.width = `${task.progress}%`;
            taskBar.appendChild(progressBar);
        }
        
        // 添加到第一个时间单元格
        const firstTimeCell = row.querySelector('.time-cell');
        if (firstTimeCell) {
            firstTimeCell.style.position = 'relative';
            firstTimeCell.appendChild(taskBar);
        }
    }

    getTimePosition(targetTime) {
        const timeColumns = this.headerRow.querySelectorAll('.time-column');
        
        for (let i = 0; i < timeColumns.length; i++) {
            const columnTime = new Date(timeColumns[i].dataset.time);
            const nextColumnTime = i < timeColumns.length - 1 ? 
                new Date(timeColumns[i + 1].dataset.time) : 
                new Date(columnTime.getTime() + 60 * 60 * 1000);
            
            if (targetTime >= columnTime && targetTime < nextColumnTime) {
                // 计算在单元格内的精确位置
                const cellProgress = (targetTime - columnTime) / (nextColumnTime - columnTime);
                return i + cellProgress;
            }
        }
        
        return -1; // 不在范围内
    }

    getTaskColorClass(color) {
        const colorMap = {
            '#e74c3c': 'color-red',
            '#27ae60': 'color-green',
            '#f39c12': 'color-orange',
            '#9b59b6': 'color-purple',
            '#1abc9c': 'color-teal'
        };
        
        return colorMap[color] || '';
    }

    formatDuration(startTime, endTime) {
        const start = new Date(startTime);
        const end = new Date(endTime);
        const duration = (end - start) / (1000 * 60 * 60); // 小时
        
        if (duration < 24) {
            return `${Math.round(duration)}小时`;
        } else {
            const days = Math.floor(duration / 24);
            const hours = Math.round(duration % 24);
            return `${days}天${hours > 0 ? hours + '小时' : ''}`;
        }
    }

    updateCurrentTimeIndicator() {
        const now = new Date();
        const position = this.getTimePosition(now);
        
        if (position !== -1) {
            // 移除现有的当前时间指示器
            const existingIndicators = this.container.querySelectorAll('.current-time');
            existingIndicators.forEach(el => el.classList.remove('current-time'));
            
            // 添加新的指示器
            const columnIndex = Math.floor(position);
            const timeColumns = this.container.querySelectorAll('.time-cell');
            
            timeColumns.forEach((cell, index) => {
                if (Math.floor(index / timeColumns.length * this.headerRow.querySelectorAll('.time-column').length) === columnIndex) {
                    cell.classList.add('current-time');
                }
            });
        }
    }

    initDragHandler() {
        this.dragHandler = new DragHandler(this);
    }

    bindEvents() {
        // 任务行点击事件
        this.tbody.addEventListener('click', (e) => {
            const row = e.target.closest('.gantt-row');
            if (row) {
                this.selectTask(row.dataset.taskId);
            }
        });
        
        // 任务条双击编辑
        this.tbody.addEventListener('dblclick', (e) => {
            const taskBar = e.target.closest('.task-bar');
            if (taskBar) {
                this.editTask(taskBar.dataset.taskId);
            }
        });
        
        // 定期更新当前时间指示器
        setInterval(() => {
            this.updateCurrentTimeIndicator();
        }, 60000); // 每分钟更新一次
    }

    selectTask(taskId) {
        // 移除之前的选择
        const prevSelected = this.container.querySelector('.gantt-row.selected');
        if (prevSelected) {
            prevSelected.classList.remove('selected');
        }
        
        // 选择新任务
        const row = this.container.querySelector(`[data-task-id="${taskId}"]`);
        if (row) {
            row.classList.add('selected');
            this.selectedTask = this.tasks.find(t => t.id == taskId);
        }
    }

    async editTask(taskId) {
        const task = this.tasks.find(t => t.id == taskId);
        if (task) {
            window.taskModal.show(task);
        }
    }

    async createTask(taskData) {
        try {
            const response = await fetch('api/tasks.php?action=create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(taskData)
            });
            
            const result = await response.json();
            
            if (result.success) {
                await this.loadTasks(); // 重新加载任务
                this.showSuccess('任务创建成功');
                return result.data;
            } else {
                this.showError('创建任务失败: ' + result.error);
                return null;
            }
        } catch (error) {
            this.showError('网络错误: ' + error.message);
            return null;
        }
    }

    async updateTask(taskId, taskData) {
        try {
            const response = await fetch(`api/tasks.php?action=update&id=${taskId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(taskData)
            });
            
            const result = await response.json();
            
            if (result.success) {
                await this.loadTasks(); // 重新加载任务
                this.showSuccess('任务更新成功');
                return true;
            } else {
                this.showError('更新任务失败: ' + result.error);
                return false;
            }
        } catch (error) {
            this.showError('网络错误: ' + error.message);
            return false;
        }
    }

    async deleteTask(taskId) {
        if (!confirm('确定要删除这个任务吗？')) {
            return false;
        }
        
        try {
            const response = await fetch(`api/tasks.php?action=delete&id=${taskId}`, {
                method: 'DELETE'
            });
            
            const result = await response.json();
            
            if (result.success) {
                await this.loadTasks(); // 重新加载任务
                this.showSuccess('任务删除成功');
                return true;
            } else {
                this.showError('删除任务失败: ' + result.error);
                return false;
            }
        } catch (error) {
            this.showError('网络错误: ' + error.message);
            return false;
        }
    }

    async moveTask(taskId, newStartTime, newEndTime) {
        try {
            const response = await fetch('api/tasks.php?action=move', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    id: taskId,
                    start_time: newStartTime,
                    end_time: newEndTime
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                // 更新本地任务数据
                const task = this.tasks.find(t => t.id == taskId);
                if (task) {
                    task.start_time = newStartTime;
                    task.end_time = newEndTime;
                }
                return true;
            } else {
                this.showError('移动任务失败: ' + result.error);
                return false;
            }
        } catch (error) {
            this.showError('网络错误: ' + error.message);
            return false;
        }
    }

    showLoading() {
        const loading = document.createElement('div');
        loading.className = 'loading';
        loading.textContent = '加载中...';
        loading.id = 'gantt-loading';
        this.container.appendChild(loading);
    }

    hideLoading() {
        const loading = document.getElementById('gantt-loading');
        if (loading) {
            loading.remove();
        }
    }

    showError(message) {
        this.showMessage(message, 'error');
    }

    showSuccess(message) {
        this.showMessage(message, 'success');
    }

    showMessage(message, type) {
        const messageEl = document.createElement('div');
        messageEl.className = type;
        messageEl.textContent = message;
        
        // 插入到容器顶部
        this.container.insertBefore(messageEl, this.container.firstChild);
        
        // 3秒后自动移除
        setTimeout(() => {
            messageEl.remove();
        }, 3000);
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 公共方法：刷新甘特图
    refresh() {
        this.loadTasks();
    }

    // 公共方法：设置时间范围
    setTimeRange(startDate, endDate) {
        this.options.startDate = startDate;
        this.options.endDate = endDate;
        this.generateTimeHeaders();
        this.renderTasks();
    }

    // 公共方法：获取选中的任务
    getSelectedTask() {
        return this.selectedTask;
    }
}
