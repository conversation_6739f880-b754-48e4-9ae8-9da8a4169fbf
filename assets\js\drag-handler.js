/**
 * 拖拽处理器
 * 处理甘特图任务条的拖拽功能
 */

class DragHandler {
    constructor(ganttChart) {
        this.gantt = ganttChart;
        this.isDragging = false;
        this.dragElement = null;
        this.dragStartX = 0;
        this.dragStartTime = null;
        this.originalStartTime = null;
        this.originalEndTime = null;
        this.dragHelper = null;
        this.timeTooltip = null;
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.createDragHelper();
        this.createTimeTooltip();
    }

    bindEvents() {
        // 鼠标按下事件
        this.gantt.container.addEventListener('mousedown', (e) => {
            const taskBar = e.target.closest('.task-bar');
            if (taskBar) {
                this.startDrag(e, taskBar);
            }
        });

        // 鼠标移动事件
        document.addEventListener('mousemove', (e) => {
            if (this.isDragging) {
                this.handleDrag(e);
            }
        });

        // 鼠标释放事件
        document.addEventListener('mouseup', (e) => {
            if (this.isDragging) {
                this.endDrag(e);
            }
        });

        // 触摸事件支持（移动设备）
        this.gantt.container.addEventListener('touchstart', (e) => {
            const taskBar = e.target.closest('.task-bar');
            if (taskBar) {
                e.preventDefault();
                const touch = e.touches[0];
                this.startDrag(touch, taskBar);
            }
        });

        document.addEventListener('touchmove', (e) => {
            if (this.isDragging) {
                e.preventDefault();
                const touch = e.touches[0];
                this.handleDrag(touch);
            }
        });

        document.addEventListener('touchend', (e) => {
            if (this.isDragging) {
                e.preventDefault();
                this.endDrag(e);
            }
        });
    }

    createDragHelper() {
        this.dragHelper = document.createElement('div');
        this.dragHelper.className = 'drag-helper';
        this.dragHelper.style.display = 'none';
        this.gantt.container.appendChild(this.dragHelper);
    }

    createTimeTooltip() {
        this.timeTooltip = document.createElement('div');
        this.timeTooltip.className = 'time-tooltip';
        this.timeTooltip.style.display = 'none';
        document.body.appendChild(this.timeTooltip);
    }

    startDrag(event, taskBar) {
        this.isDragging = true;
        this.dragElement = taskBar;
        this.dragStartX = event.clientX || event.pageX;
        
        // 获取任务信息
        const taskId = taskBar.dataset.taskId;
        const task = this.gantt.tasks.find(t => t.id == taskId);
        
        if (task) {
            this.originalStartTime = new Date(task.start_time);
            this.originalEndTime = new Date(task.end_time);
            this.dragStartTime = new Date(this.originalStartTime);
        }

        // 添加拖拽样式
        taskBar.classList.add('dragging');
        
        // 显示拖拽辅助线
        this.showDragHelper(event);
        
        // 显示时间提示
        this.showTimeTooltip(event, this.originalStartTime, this.originalEndTime);
        
        // 阻止默认行为
        event.preventDefault();
        
        // 设置光标样式
        document.body.style.cursor = 'grabbing';
    }

    handleDrag(event) {
        if (!this.isDragging || !this.dragElement) return;

        const currentX = event.clientX || event.pageX;
        const deltaX = currentX - this.dragStartX;
        
        // 计算时间偏移
        const timeOffset = this.calculateTimeOffset(deltaX);
        
        // 计算新的开始和结束时间
        const newStartTime = new Date(this.originalStartTime.getTime() + timeOffset);
        const newEndTime = new Date(this.originalEndTime.getTime() + timeOffset);
        
        // 更新任务条位置
        this.updateTaskBarPosition(newStartTime, newEndTime);
        
        // 更新拖拽辅助线
        this.updateDragHelper(event);
        
        // 更新时间提示
        this.updateTimeTooltip(event, newStartTime, newEndTime);
    }

    endDrag(event) {
        if (!this.isDragging || !this.dragElement) return;

        const currentX = event.clientX || event.pageX || this.dragStartX;
        const deltaX = currentX - this.dragStartX;
        
        // 计算最终时间
        const timeOffset = this.calculateTimeOffset(deltaX);
        const newStartTime = new Date(this.originalStartTime.getTime() + timeOffset);
        const newEndTime = new Date(this.originalEndTime.getTime() + timeOffset);
        
        // 对齐到小时
        this.alignToHour(newStartTime);
        this.alignToHour(newEndTime);
        
        // 保存更改
        const taskId = this.dragElement.dataset.taskId;
        this.saveTaskMove(taskId, newStartTime, newEndTime);
        
        // 清理拖拽状态
        this.cleanup();
    }

    calculateTimeOffset(deltaX) {
        // 每个像素代表的时间（毫秒）
        const pixelsPerHour = this.gantt.options.cellWidth;
        const hoursOffset = deltaX / pixelsPerHour;
        return hoursOffset * 60 * 60 * 1000; // 转换为毫秒
    }

    updateTaskBarPosition(startTime, endTime) {
        const startPosition = this.gantt.getTimePosition(startTime);
        const endPosition = this.gantt.getTimePosition(endTime);
        
        if (startPosition !== -1 && endPosition !== -1) {
            const left = startPosition * this.gantt.options.cellWidth;
            const width = Math.max((endPosition - startPosition) * this.gantt.options.cellWidth, 30);
            
            this.dragElement.style.left = `${left}px`;
            this.dragElement.style.width = `${width}px`;
        }
    }

    showDragHelper(event) {
        const rect = this.gantt.container.getBoundingClientRect();
        const x = (event.clientX || event.pageX) - rect.left;
        
        this.dragHelper.style.left = `${x}px`;
        this.dragHelper.style.display = 'block';
    }

    updateDragHelper(event) {
        const rect = this.gantt.container.getBoundingClientRect();
        const x = (event.clientX || event.pageX) - rect.left;
        
        this.dragHelper.style.left = `${x}px`;
    }

    showTimeTooltip(event, startTime, endTime) {
        const content = this.formatTimeRange(startTime, endTime);
        this.timeTooltip.textContent = content;
        this.updateTooltipPosition(event);
        this.timeTooltip.style.display = 'block';
    }

    updateTimeTooltip(event, startTime, endTime) {
        const content = this.formatTimeRange(startTime, endTime);
        this.timeTooltip.textContent = content;
        this.updateTooltipPosition(event);
    }

    updateTooltipPosition(event) {
        const x = (event.clientX || event.pageX) + 10;
        const y = (event.clientY || event.pageY) - 30;
        
        this.timeTooltip.style.left = `${x}px`;
        this.timeTooltip.style.top = `${y}px`;
    }

    formatTimeRange(startTime, endTime) {
        const formatTime = (date) => {
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hour = String(date.getHours()).padStart(2, '0');
            const minute = String(date.getMinutes()).padStart(2, '0');
            return `${month}/${day} ${hour}:${minute}`;
        };
        
        return `${formatTime(startTime)} - ${formatTime(endTime)}`;
    }

    alignToHour(date) {
        // 对齐到最近的小时
        const minutes = date.getMinutes();
        if (minutes >= 30) {
            date.setHours(date.getHours() + 1);
        }
        date.setMinutes(0);
        date.setSeconds(0);
        date.setMilliseconds(0);
    }

    async saveTaskMove(taskId, newStartTime, newEndTime) {
        const startTimeStr = this.formatDateTime(newStartTime);
        const endTimeStr = this.formatDateTime(newEndTime);
        
        const success = await this.gantt.moveTask(taskId, startTimeStr, endTimeStr);
        
        if (!success) {
            // 如果保存失败，恢复原始位置
            this.updateTaskBarPosition(this.originalStartTime, this.originalEndTime);
        }
    }

    formatDateTime(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hour = String(date.getHours()).padStart(2, '0');
        const minute = String(date.getMinutes()).padStart(2, '0');
        const second = String(date.getSeconds()).padStart(2, '0');
        
        return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
    }

    cleanup() {
        this.isDragging = false;
        
        if (this.dragElement) {
            this.dragElement.classList.remove('dragging');
            this.dragElement = null;
        }
        
        this.dragHelper.style.display = 'none';
        this.timeTooltip.style.display = 'none';
        
        document.body.style.cursor = '';
        
        this.dragStartX = 0;
        this.dragStartTime = null;
        this.originalStartTime = null;
        this.originalEndTime = null;
    }

    // 公共方法：启用/禁用拖拽
    setEnabled(enabled) {
        this.enabled = enabled;
        
        const taskBars = this.gantt.container.querySelectorAll('.task-bar');
        taskBars.forEach(bar => {
            bar.style.cursor = enabled ? 'move' : 'default';
        });
    }

    // 公共方法：检查是否正在拖拽
    isDraggingActive() {
        return this.isDragging;
    }
}
