/**
 * 甘特图样式文件
 * 响应式设计，支持桌面和移动设备
 */

/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f7fa;
    color: #333;
    line-height: 1.6;
}

/* 容器样式 */
.container {
    max-width: 100%;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.header h1 {
    font-size: 2rem;
    font-weight: 300;
    margin-bottom: 10px;
}

.header p {
    opacity: 0.9;
    font-size: 1.1rem;
}

/* 工具栏样式 */
.toolbar {
    background: white;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    align-items: center;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: #3498db;
    color: white;
}

.btn-primary:hover {
    background: #2980b9;
    transform: translateY(-1px);
}

.btn-success {
    background: #27ae60;
    color: white;
}

.btn-success:hover {
    background: #229954;
    transform: translateY(-1px);
}

.btn-danger {
    background: #e74c3c;
    color: white;
}

.btn-danger:hover {
    background: #c0392b;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background: #7f8c8d;
    transform: translateY(-1px);
}

/* 甘特图容器 */
.gantt-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.gantt-wrapper {
    position: relative;
    overflow: auto;
    max-height: 70vh;
}

/* 甘特图表格 */
.gantt-table {
    width: 100%;
    border-collapse: collapse;
    min-width: 1200px;
}

.gantt-header {
    background: #f8f9fa;
    position: sticky;
    top: 0;
    z-index: 10;
}

.gantt-header th {
    padding: 12px 8px;
    text-align: center;
    border: 1px solid #dee2e6;
    font-weight: 600;
    font-size: 12px;
    color: #495057;
    white-space: nowrap;
}

.gantt-header .task-column {
    width: 200px;
    text-align: left;
    background: #e9ecef;
}

.gantt-header .time-column {
    width: 60px;
    min-width: 60px;
}

/* 任务行样式 */
.gantt-row {
    border-bottom: 1px solid #dee2e6;
    transition: background-color 0.2s ease;
}

.gantt-row:hover {
    background-color: #f8f9fa;
}

.gantt-row.selected {
    background-color: #e3f2fd;
}

.gantt-cell {
    padding: 8px;
    border: 1px solid #dee2e6;
    position: relative;
    height: 40px;
    vertical-align: middle;
}

.task-info {
    width: 200px;
    padding: 8px 12px;
    background: #f8f9fa;
    border-right: 2px solid #dee2e6;
    font-weight: 500;
    color: #495057;
}

.task-name {
    font-size: 14px;
    margin-bottom: 2px;
}

.task-duration {
    font-size: 11px;
    color: #6c757d;
}

/* 时间单元格 */
.time-cell {
    width: 60px;
    min-width: 60px;
    border-right: 1px solid #e9ecef;
    position: relative;
}

.time-cell.current-time {
    background-color: #fff3cd;
    border-left: 2px solid #ffc107;
}

.time-cell.weekend {
    background-color: #f8f9fa;
}

/* 任务条样式 */
.task-bar {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    height: 24px;
    background: linear-gradient(135deg, #3498db, #2980b9);
    border-radius: 12px;
    cursor: move;
    display: flex;
    align-items: center;
    padding: 0 8px;
    color: white;
    font-size: 11px;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
    z-index: 5;
    min-width: 30px;
    overflow: hidden;
    white-space: nowrap;
}

.task-bar:hover {
    transform: translateY(-50%) scale(1.02);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    z-index: 10;
}

.task-bar.dragging {
    opacity: 0.8;
    transform: translateY(-50%) scale(1.05);
    z-index: 15;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
}

/* 任务条颜色变体 */
.task-bar.color-red {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.task-bar.color-green {
    background: linear-gradient(135deg, #27ae60, #229954);
}

.task-bar.color-orange {
    background: linear-gradient(135deg, #f39c12, #e67e22);
}

.task-bar.color-purple {
    background: linear-gradient(135deg, #9b59b6, #8e44ad);
}

.task-bar.color-teal {
    background: linear-gradient(135deg, #1abc9c, #16a085);
}

/* 进度条 */
.task-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 0 0 12px 12px;
    transition: width 0.3s ease;
}

/* 拖拽辅助线 */
.drag-helper {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #3498db;
    opacity: 0.7;
    z-index: 20;
    pointer-events: none;
}

/* 时间提示 */
.time-tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    white-space: nowrap;
    z-index: 25;
    pointer-events: none;
    transform: translateX(-50%);
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    animation: fadeIn 0.3s ease;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 8px;
    padding: 20px;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
    animation: slideIn 0.3s ease;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #dee2e6;
}

.modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #495057;
}

.close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6c757d;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close:hover {
    color: #495057;
}

/* 表单样式 */
.form-group {
    margin-bottom: 15px;
}

.form-label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #495057;
}

.form-control {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.form-row {
    display: flex;
    gap: 15px;
}

.form-row .form-group {
    flex: 1;
}

/* 颜色选择器 */
.color-picker {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    margin-top: 5px;
}

.color-option {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    border: 2px solid transparent;
    cursor: pointer;
    transition: all 0.2s ease;
}

.color-option:hover {
    transform: scale(1.1);
}

.color-option.selected {
    border-color: #495057;
    transform: scale(1.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .header h1 {
        font-size: 1.5rem;
    }
    
    .toolbar {
        flex-direction: column;
        align-items: stretch;
    }
    
    .btn {
        justify-content: center;
    }
    
    .gantt-wrapper {
        max-height: 60vh;
    }
    
    .task-info {
        width: 150px;
    }
    
    .gantt-header .task-column {
        width: 150px;
    }
    
    .time-cell {
        width: 40px;
        min-width: 40px;
    }
    
    .gantt-header .time-column {
        width: 40px;
        min-width: 40px;
    }
    
    .task-bar {
        height: 20px;
        font-size: 10px;
        padding: 0 6px;
    }
    
    .modal-content {
        width: 95%;
        margin: 10px;
    }
    
    .form-row {
        flex-direction: column;
        gap: 0;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 加载状态 */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: #6c757d;
}

.loading::after {
    content: '';
    width: 20px;
    height: 20px;
    border: 2px solid #dee2e6;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 错误状态 */
.error {
    background: #f8d7da;
    color: #721c24;
    padding: 15px;
    border-radius: 4px;
    margin: 10px 0;
    border: 1px solid #f5c6cb;
}

/* 成功状态 */
.success {
    background: #d4edda;
    color: #155724;
    padding: 15px;
    border-radius: 4px;
    margin: 10px 0;
    border: 1px solid #c3e6cb;
}
