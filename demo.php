<?php
/**
 * 甘特图演示页面
 * 无需数据库，展示前端功能
 */
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>甘特图管理系统 - 演示版</title>
    <link rel="stylesheet" href="assets/css/gantt.css">
    <style>
        .demo-notice {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .demo-notice h3 {
            margin: 0 0 10px 0;
        }
        
        .demo-notice p {
            margin: 0;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 演示提醒 -->
        <div class="demo-notice">
            <h3>🎭 演示模式</h3>
            <p>这是甘特图系统的前端演示版本，展示界面和交互功能。完整功能需要配置数据库。</p>
        </div>

        <!-- 页面头部 -->
        <div class="header">
            <div class="page-header">
                <div class="page-title">
                    <h1>甘特图管理系统</h1>
                    <p>专业的项目时间管理工具 - 精确到小时级别</p>
                </div>
                <div class="page-actions">
                    <span style="color: rgba(255,255,255,0.8); font-size: 0.9rem;">
                        演示版本 v1.0.0
                    </span>
                </div>
            </div>
        </div>

        <!-- 工具栏 -->
        <div class="toolbar">
            <button class="btn btn-primary" onclick="showDemoModal()">
                ➕ 新建任务 (演示)
            </button>
            <button class="btn btn-success" onclick="showMessage('演示模式下无法刷新数据', 'info')">
                🔄 刷新
            </button>
            <button class="btn btn-secondary" onclick="exportDemoData()">
                📤 导出演示数据
            </button>
            <button class="btn btn-secondary" onclick="showMessage('演示模式下时间范围固定', 'info')">
                📅 时间范围
            </button>
            
            <div style="margin-left: auto; display: flex; gap: 10px; align-items: center;">
                <span style="color: #6c757d; font-size: 0.9rem;">
                    当前时间: <span id="current-time"></span>
                </span>
            </div>
        </div>

        <!-- 甘特图容器 -->
        <div class="gantt-container">
            <div class="gantt-wrapper">
                <table class="gantt-table">
                    <thead class="gantt-header">
                        <tr>
                            <th class="task-column">任务名称</th>
                            <th class="time-column">01/15<br>09:00</th>
                            <th class="time-column">10:00</th>
                            <th class="time-column">11:00</th>
                            <th class="time-column">12:00</th>
                            <th class="time-column">13:00</th>
                            <th class="time-column">14:00</th>
                            <th class="time-column">15:00</th>
                            <th class="time-column">16:00</th>
                            <th class="time-column">17:00</th>
                            <th class="time-column">01/16<br>09:00</th>
                            <th class="time-column">10:00</th>
                            <th class="time-column">11:00</th>
                            <th class="time-column">12:00</th>
                            <th class="time-column">13:00</th>
                            <th class="time-column">14:00</th>
                            <th class="time-column">15:00</th>
                            <th class="time-column">16:00</th>
                            <th class="time-column">17:00</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 示例任务1 -->
                        <tr class="gantt-row">
                            <td class="task-info">
                                <div class="task-name">项目启动</div>
                                <div class="task-duration">8小时</div>
                            </td>
                            <td class="gantt-cell time-cell" style="position: relative;">
                                <div class="task-bar color-green" style="left: 0px; width: 480px;" onclick="showTaskInfo('项目启动')">
                                    项目启动
                                    <div class="task-progress" style="width: 100%;"></div>
                                </div>
                            </td>
                            <td class="gantt-cell time-cell"></td>
                            <td class="gantt-cell time-cell"></td>
                            <td class="gantt-cell time-cell"></td>
                            <td class="gantt-cell time-cell"></td>
                            <td class="gantt-cell time-cell"></td>
                            <td class="gantt-cell time-cell"></td>
                            <td class="gantt-cell time-cell"></td>
                            <td class="gantt-cell time-cell"></td>
                            <td class="gantt-cell time-cell"></td>
                            <td class="gantt-cell time-cell"></td>
                            <td class="gantt-cell time-cell"></td>
                            <td class="gantt-cell time-cell"></td>
                            <td class="gantt-cell time-cell"></td>
                            <td class="gantt-cell time-cell"></td>
                            <td class="gantt-cell time-cell"></td>
                            <td class="gantt-cell time-cell"></td>
                            <td class="gantt-cell time-cell"></td>
                        </tr>
                        
                        <!-- 示例任务2 -->
                        <tr class="gantt-row">
                            <td class="task-info">
                                <div class="task-name">系统设计</div>
                                <div class="task-duration">2天</div>
                            </td>
                            <td class="gantt-cell time-cell"></td>
                            <td class="gantt-cell time-cell"></td>
                            <td class="gantt-cell time-cell"></td>
                            <td class="gantt-cell time-cell"></td>
                            <td class="gantt-cell time-cell"></td>
                            <td class="gantt-cell time-cell"></td>
                            <td class="gantt-cell time-cell"></td>
                            <td class="gantt-cell time-cell"></td>
                            <td class="gantt-cell time-cell"></td>
                            <td class="gantt-cell time-cell" style="position: relative;">
                                <div class="task-bar" style="left: 0px; width: 480px;" onclick="showTaskInfo('系统设计')">
                                    系统设计
                                    <div class="task-progress" style="width: 75%;"></div>
                                </div>
                            </td>
                            <td class="gantt-cell time-cell"></td>
                            <td class="gantt-cell time-cell"></td>
                            <td class="gantt-cell time-cell"></td>
                            <td class="gantt-cell time-cell"></td>
                            <td class="gantt-cell time-cell"></td>
                            <td class="gantt-cell time-cell"></td>
                            <td class="gantt-cell time-cell"></td>
                            <td class="gantt-cell time-cell"></td>
                        </tr>
                        
                        <!-- 示例任务3 -->
                        <tr class="gantt-row">
                            <td class="task-info">
                                <div class="task-name">前端开发</div>
                                <div class="task-duration">5天</div>
                            </td>
                            <td class="gantt-cell time-cell"></td>
                            <td class="gantt-cell time-cell"></td>
                            <td class="gantt-cell time-cell"></td>
                            <td class="gantt-cell time-cell"></td>
                            <td class="gantt-cell time-cell" style="position: relative;">
                                <div class="task-bar color-orange" style="left: 0px; width: 420px;" onclick="showTaskInfo('前端开发')">
                                    前端开发
                                    <div class="task-progress" style="width: 50%;"></div>
                                </div>
                            </td>
                            <td class="gantt-cell time-cell"></td>
                            <td class="gantt-cell time-cell"></td>
                            <td class="gantt-cell time-cell"></td>
                            <td class="gantt-cell time-cell"></td>
                            <td class="gantt-cell time-cell"></td>
                            <td class="gantt-cell time-cell"></td>
                            <td class="gantt-cell time-cell"></td>
                            <td class="gantt-cell time-cell"></td>
                            <td class="gantt-cell time-cell"></td>
                            <td class="gantt-cell time-cell"></td>
                            <td class="gantt-cell time-cell"></td>
                            <td class="gantt-cell time-cell"></td>
                            <td class="gantt-cell time-cell"></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 演示模态框 -->
    <div id="demo-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">演示功能</h3>
                <button class="close" onclick="hideDemoModal()">&times;</button>
            </div>
            <div style="padding: 20px;">
                <p>这是甘特图系统的演示版本，展示了以下功能：</p>
                <ul>
                    <li>✅ 响应式甘特图界面</li>
                    <li>✅ 任务条显示和进度指示</li>
                    <li>✅ 时间轴小时级精度</li>
                    <li>✅ 现代化UI设计</li>
                    <li>⚠️ 拖拽功能（需要数据库支持）</li>
                    <li>⚠️ 数据保存（需要数据库支持）</li>
                </ul>
                
                <h4>完整功能体验：</h4>
                <p>要体验完整功能，请：</p>
                <ol>
                    <li>安装Docker Desktop并启动</li>
                    <li>运行 <code>.\start-docker.bat</code></li>
                    <li>访问 <code>http://localhost:8080</code></li>
                </ol>
                
                <div style="text-align: center; margin-top: 20px;">
                    <button class="btn btn-primary" onclick="hideDemoModal()">了解了</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 更新当前时间
        function updateCurrentTime() {
            const now = new Date();
            const timeStr = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('current-time').textContent = timeStr;
        }
        
        // 显示消息
        function showMessage(message, type = 'info') {
            const messageEl = document.createElement('div');
            messageEl.className = type === 'info' ? 'install-step' : type;
            messageEl.textContent = message;
            messageEl.style.position = 'fixed';
            messageEl.style.top = '20px';
            messageEl.style.right = '20px';
            messageEl.style.zIndex = '9999';
            messageEl.style.maxWidth = '300px';
            
            document.body.appendChild(messageEl);
            
            setTimeout(() => {
                messageEl.remove();
            }, 3000);
        }
        
        // 显示任务信息
        function showTaskInfo(taskName) {
            showMessage(`点击了任务: ${taskName}`, 'success');
        }
        
        // 演示模态框
        function showDemoModal() {
            document.getElementById('demo-modal').classList.add('show');
        }
        
        function hideDemoModal() {
            document.getElementById('demo-modal').classList.remove('show');
        }
        
        // 导出演示数据
        function exportDemoData() {
            const demoData = [
                {
                    id: 1,
                    name: "项目启动",
                    description: "项目启动会议和需求分析",
                    start_time: "2024-01-15 09:00:00",
                    end_time: "2024-01-15 17:00:00",
                    progress: 100,
                    color: "#27ae60"
                },
                {
                    id: 2,
                    name: "系统设计",
                    description: "系统架构设计和数据库设计",
                    start_time: "2024-01-16 09:00:00",
                    end_time: "2024-01-16 17:00:00",
                    progress: 75,
                    color: "#3498db"
                },
                {
                    id: 3,
                    name: "前端开发",
                    description: "用户界面开发和交互功能实现",
                    start_time: "2024-01-15 13:00:00",
                    end_time: "2024-01-15 20:00:00",
                    progress: 50,
                    color: "#f39c12"
                }
            ];
            
            const dataStr = JSON.stringify(demoData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `gantt-demo-data-${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            
            showMessage('演示数据已导出', 'success');
        }
        
        // 初始化
        updateCurrentTime();
        setInterval(updateCurrentTime, 1000);
        
        // 模态框外部点击关闭
        document.getElementById('demo-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideDemoModal();
            }
        });
    </script>
</body>
</html>
